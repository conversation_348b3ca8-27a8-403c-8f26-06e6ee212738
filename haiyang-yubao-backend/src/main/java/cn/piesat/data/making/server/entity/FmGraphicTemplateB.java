package cn.piesat.data.making.server.entity;


import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 图形模板表实体类
 *
 * <AUTHOR>
 * @date 2024-09-21 15:07:39
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_template_b")
public class FmGraphicTemplateB implements Serializable {

    private static final long serialVersionUID = 458593465265557600L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * 名称
     */
    @TableField("parent_id")
    private Long parentId;
    /**
     * 产品类型
     */
    @TableField("product_type")
    private String productType;
    /**
     * 状态
     */
    @TableField("status")
    private Boolean status;
    /**
     * 左上纬度
     */
    @TableField("up_latitude")
    private Double leftLongitude;
    /**
     * 左下纬度
     */
    @TableField("down_latitude")
    private Double leftLatitude;
    /**
     * 右上经度
     */
    @TableField("left_longitude")
    private Double rightLongitude;
    /**
     * 右下经度
     */
    @TableField("right_longitude")
    private Double rightLatitude;
    /**
     * 标签
     */
    @TableField("tag")
    private String tag;
    /**
     * 图片路径
     */
    @TableField("image_url")
    private String imageUrl;
    /**
     * 工具
     */
    @TableField("tool")
    private String tool;
    /**
     * 创建人id
     */
    @TableField("create_user_id")
    private Long createUserId;
    /**
     * 创建人
     */
    @TableField("create_user")
    private String createUser;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 更新人id
     */
    @TableField("update_user_id")
    private Long updateUserId;
    /**
     * 更新人
     */
    @TableField("update_user")
    private String updateUser;
    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 模版类型
     */
    @TableField("template_type")
    private String templateType;
    /**
     * 标签列表
     */
    @TableField(exist = false)
    private List<FmGraphicTmplateSign> sign;
    /**
     * 工具列表
     */
    @TableField(exist = false)
    private List<FmGraphicTemplateTools> tools;
    /**
     * 下级工具
     */
    @TableField(exist = false)
    private List<FmGraphicTemplateB> list;

    public List<FmGraphicTemplateB> getList() {
        return list;
    }

    public void setList(List<FmGraphicTemplateB> list) {
        this.list = list;
    }

    public List<FmGraphicTmplateSign> getSign() {
        return sign;
    }

    public void setSign(List<FmGraphicTmplateSign> sign) {
        this.sign = sign;
    }

    public List<FmGraphicTemplateTools> getTools() {
        return tools;
    }

    public void setTools(List<FmGraphicTemplateTools> tools) {
        this.tools = tools;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Double getLeftLongitude() {
        return leftLongitude;
    }

    public void setLeftLongitude(Double leftLongitude) {
        this.leftLongitude = leftLongitude;
    }

    public Double getLeftLatitude() {
        return leftLatitude;
    }

    public void setLeftLatitude(Double leftLatitude) {
        this.leftLatitude = leftLatitude;
    }

    public Double getRightLongitude() {
        return rightLongitude;
    }

    public void setRightLongitude(Double rightLongitude) {
        this.rightLongitude = rightLongitude;
    }

    public Double getRightLatitude() {
        return rightLatitude;
    }

    public void setRightLatitude(Double rightLatitude) {
        this.rightLatitude = rightLatitude;
    }

    public String getTag() {
        return tag;
    }

    public void setTag(String tag) {
        this.tag = tag;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getTool() {
        return tool;
    }

    public void setTool(String tool) {
        this.tool = tool;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getCreateUser() {
        return createUser;
    }

    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateUserId() {
        return updateUserId;
    }

    public void setUpdateUserId(Long updateUserId) {
        this.updateUserId = updateUserId;
    }

    public String getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(String updateUser) {
        this.updateUser = updateUser;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
