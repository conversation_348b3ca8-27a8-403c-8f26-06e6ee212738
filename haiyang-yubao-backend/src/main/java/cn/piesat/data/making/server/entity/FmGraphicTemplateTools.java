package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-09 16:05:18
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_template_tools")
public class FmGraphicTemplateTools implements Serializable {

    private static final long serialVersionUID = 674128664044567916L;


    @TableField("id")
    private Long id;
    @TableField("type")
    private String type;
    @TableField("name")
    private String name;
    @TableField("context")
    private String context;
    @TableField("template_id")
    private Long templateId;
    @TableField("url")
    private String url;
    @TableField("active_url")
    private String activeUrl;

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getActiveUrl() {
        return activeUrl;
    }

    public void setActiveUrl(String activeUrl) {
        this.activeUrl = activeUrl;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }
}
