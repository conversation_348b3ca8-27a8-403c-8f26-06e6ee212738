package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报记录表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_forecast_record_b")
public class ForecastRecord implements Serializable {

    private static final long serialVersionUID = 760123462074351302L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 预报任务id
     **/
    @TableField("forecast_task_id")
    private Long forecastTaskId;
    /**
     * 预报模板id
     **/
    @TableField("forecast_template_id")
    private Long forecastTemplateId;
    /**
     * 预报模板编码
     **/
    @TableField("forecast_template_code")
    private String forecastTemplateCode;
    /**
     * 预报类型
     **/
    @TableField("forecast_type")
    private String forecastType;
    /**
     * 起报时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("report_time")
    private Date reportTime;
    /**
     * 数据源
     **/
    @TableField("data_source")
    private String dataSource;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 海区、城市预报制作-预报内容
     */
    @TableField("forecast_content")
    private String forecastContent;
}



