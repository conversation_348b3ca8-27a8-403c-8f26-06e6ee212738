package cn.piesat.data.making.server.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmGraphicSign;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-10 10:30:37
 */
public interface FmGraphicSignDao extends BaseMapper<FmGraphicSign> {
    @Select("SELECT * FROM fm_graphic_sign")
    List<FmGraphicSign> selectAll();
    @Delete("DELETE  FROM fm_graphic_sign where template_id = #{mainId}")
    void deleteByTemplateId(Long templateId);
}
