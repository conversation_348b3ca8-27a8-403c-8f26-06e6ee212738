package cn.piesat.data.making.server.event;

import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.entity.ForecastProductRecord;
import cn.piesat.data.making.server.entity.ForecastProductTemplate;
import cn.piesat.data.making.server.service.ForecastProductRecordService;
import cn.piesat.data.making.server.service.ForecastProductTemplateService;
import cn.piesat.data.making.server.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;

@Component
@Slf4j
public class OnlyOfficeEventListener {

    @Resource
    private ForecastProductTemplateService forecastProductTemplateService;
    @Resource
    private ForecastProductRecordService forecastProductRecordService;

    @EventListener
    public void saveFile(OnlyOfficeFile onlyOfficeFile) throws IOException {
        log.info("监听到文件保存事件:{}", JsonUtil.object2Json(onlyOfficeFile));
        Long id = onlyOfficeFile.getId();
        String type = onlyOfficeFile.getType();
        String filePath = onlyOfficeFile.getFilePath();
        String fileUrl;
        //修改预报产品word
        if ("record".equals(type)) {
            ForecastProductRecord record = forecastProductRecordService.getById(id);
            fileUrl = record.getFileUrl();
        } else {
            //修改产品模板word
            ForecastProductTemplate template = forecastProductTemplateService.getById(id);
            fileUrl = template.getFileUrl();
        }
        FileUtil.copyFile(filePath, fileUrl);
    }
}