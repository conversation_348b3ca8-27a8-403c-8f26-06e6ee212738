package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点-海浪特征值-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_hour_wv_o")
public class OceanStationHourWvO implements Serializable {

    private static final long serialVersionUID = -18323647429947977L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 波浪采样间隔
     **/
    @JsonProperty("wave_sampling_interval")
    @TableField("wave_sampling_interval")
    private String waveSamplingInterval;
    /**
     * 波浪采样间隔质控符
     **/
    @JsonProperty("wave_sampling_interval_Qc2")
    @TableField("wave_sampling_interval_qc2")
    private Integer waveSamplingIntervalQc2;
    /**
     * 平均波高
     **/
    @JsonProperty("average_wave_height")
    @TableField("average_wave_height")
    private String averageWaveHeight;
    /**
     * 平均波高质控符
     **/
    @JsonProperty("average_wave_height_Qc2")
    @TableField("average_wave_height_qc2")
    private Integer averageWaveHeightQc2;
    /**
     * 平均周期
     **/
    @JsonProperty("average_cycle")
    @TableField("average_cycle")
    private String averageCycle;
    /**
     * 平均周期质控符
     **/
    @JsonProperty("average_cycle_Qc2")
    @TableField("average_cycle_qc2")
    private Integer averageCycleQc2;
    /**
     * 最大波高
     **/
    @JsonProperty("maximum_wave_height")
    @TableField("maximum_wave_height")
    private String maximumWaveHeight;
    /**
     * 最大波高质控符
     **/
    @JsonProperty("maximum_wave_height_Qc2")
    @TableField("maximum_wave_height_qc2")
    private Integer maximumWaveHeightQc2;
    /**
     * 最大周期
     **/
    @JsonProperty("maximum_cycle")
    @TableField("maximum_cycle")
    private String maximumCycle;
    /**
     * 最大周期质控符
     **/
    @JsonProperty("maximum_cycle_Qc2")
    @TableField("maximum_cycle_qc2")
    private Integer maximumCycleQc2;
    /**
     * 十分之一波高
     **/
    @JsonProperty("one_tenth_wave_high")
    @TableField("one_tenth_wave_high")
    private String oneTenthWaveHigh;
    /**
     * 十分之一波高质控符
     **/
    @JsonProperty("one_tenth_wave_high_Qc2")
    @TableField("one_tenth_wave_high_qc2")
    private Integer oneTenthWaveHighQc2;
    /**
     * 十分之一周期
     **/
    @JsonProperty("one_tenth_cycle")
    @TableField("one_tenth_cycle")
    private String oneTenthCycle;
    /**
     * 十分之一周期质控符
     **/
    @JsonProperty("one_tenth_cycle_Qc2")
    @TableField("one_tenth_cycle_qc2")
    private Integer oneTenthCycleQc2;
    /**
     * 三分之一波高
     **/
    @JsonProperty("one_third_wave_high")
    @TableField("one_third_wave_high")
    private String oneThirdWaveHigh;
    /**
     * 三分之一波高质控符
     **/
    @JsonProperty("one_third_wave_high_Qc2")
    @TableField("one_third_wave_high_qc2")
    private Integer oneThirdWaveHighQc2;
    /**
     * 三分之一周期
     **/
    @JsonProperty("one_third_cycle")
    @TableField("one_third_cycle")
    private String oneThirdCycle;
    /**
     * 三分之一周期质控符
     **/
    @JsonProperty("one_third_cycle_Qc2")
    @TableField("one_third_cycle_qc2")
    private Integer oneThirdCycleQc2;
    /**
     * 波数
     **/
    @JsonProperty("wave_number")
    @TableField("wave_number")
    private String waveNumber;
    /**
     * 波数质控符
     **/
    @JsonProperty("wave_number_Qc2")
    @TableField("wave_number_qc2")
    private Integer waveNumberQc2;
    /**
     * 波向
     **/
    @JsonProperty("wave_direction")
    @TableField("wave_direction")
    private String waveDirection;
    /**
     * 波向质控符
     **/
    @JsonProperty("wave_direction_Qc2")
    @TableField("wave_direction_qc2")
    private Integer waveDirectionQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



