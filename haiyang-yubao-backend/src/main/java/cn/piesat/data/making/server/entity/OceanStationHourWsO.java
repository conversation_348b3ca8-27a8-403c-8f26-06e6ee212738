package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点-从测量日界开始十分钟风速风向-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_hour_ws_o")
public class OceanStationHourWsO implements Serializable {

    private static final long serialVersionUID = 770820965465208408L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 区站号
     **/
    @JsonProperty("Station_Num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 监测日期
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 小时
     **/
    @JsonProperty("Hour")
    @TableField("hour")
    private String hour;
    /**
     * 小时质控符
     **/
    @JsonProperty("Hour_Qc2")
    @TableField("hour_qc2")
    private Integer hourQc2;
    /**
     * 50分钟风向测值
     **/
    @JsonProperty("wind_dir_10")
    @TableField("wind_dir_10")
    private String windDir10;
    /**
     * 50分钟风向测值质控符
     **/
    @JsonProperty("wind_dir_10_Qc2")
    @TableField("wind_dir_10_qc2")
    private Integer windDir10Qc2;
    /**
     * 50分钟风速测值
     **/
    @JsonProperty("wind_speed_10")
    @TableField("wind_speed_10")
    private String windSpeed10;
    /**
     * 50分钟风速测值质控符
     **/
    @JsonProperty("wind_speed_10_Qc2")
    @TableField("wind_speed_10_qc2")
    private Integer windSpeed10Qc2;
    @JsonProperty("wind_dir_20")
    @TableField("wind_dir_20")
    private String windDir20;
    @JsonProperty("wind_dir_20_Qc2")
    @TableField("wind_dir_20_qc2")
    private Integer windDir20Qc2;
    @JsonProperty("wind_speed_20")
    @TableField("wind_speed_20")
    private String windSpeed20;
    @JsonProperty("wind_speed_20_Qc2")
    @TableField("wind_speed_20_qc2")
    private Integer windSpeed20Qc2;
    @JsonProperty("wind_dir_30")
    @TableField("wind_dir_30")
    private String windDir30;
    @JsonProperty("wind_dir_30_Qc2")
    @TableField("wind_dir_30_qc2")
    private Integer windDir30Qc2;
    @JsonProperty("wind_speed_30")
    @TableField("wind_speed_30")
    private String windSpeed30;
    @JsonProperty("wind_speed_30_Qc2")
    @TableField("wind_speed_30_qc2")
    private Integer windSpeed30Qc2;
    @JsonProperty("wind_dir_40")
    @TableField("wind_dir_40")
    private String windDir40;
    @JsonProperty("wind_dir_40_Qc2")
    @TableField("wind_dir_40_qc2")
    private Integer windDir40Qc2;
    @JsonProperty("wind_speed_40")
    @TableField("wind_speed_40")
    private String windSpeed40;
    @JsonProperty("wind_speed_40_Qc2")
    @TableField("wind_speed_40_qc2")
    private Integer windSpeed40Qc2;
    @JsonProperty("wind_dir_50")
    @TableField("wind_dir_50")
    private String windDir50;
    @JsonProperty("wind_dir_50_Qc2")
    @TableField("wind_dir_50_qc2")
    private Integer windDir50Qc2;
    @JsonProperty("wind_speed_50")
    @TableField("wind_speed_50")
    private String windSpeed50;
    @JsonProperty("wind_speed_50_Qc2")
    @TableField("wind_speed_50_qc2")
    private Integer windSpeed50Qc2;
    /**
     * 00分钟风向测值
     **/
    @JsonProperty("wind_dir_00")
    @TableField("wind_dir_00")
    private String windDir00;
    /**
     * 00分钟风向测值质控符
     **/
    @JsonProperty("wind_dir_00_Qc2")
    @TableField("wind_dir_00_qc2")
    private Integer windDir00Qc2;
    /**
     * 00分钟风速测值
     **/
    @JsonProperty("wind_speed_00")
    @TableField("wind_speed_00")
    private String windSpeed00;
    /**
     * 00分钟风速测值质控符
     **/
    @JsonProperty("wind_speed_00_Qc2")
    @TableField("wind_speed_00_qc2")
    private Integer windSpeed00Qc2;
    /**
     * 20-23极大风对应的风向
     **/
    @JsonProperty("wind_dir_20_23")
    @TableField("wind_dir_20_23")
    private String windDir2023;
    /**
     * 20-23极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_20_23_Qc2")
    @TableField("wind_dir_20_23_qc2")
    private Integer windDir2023Qc2;
    /**
     * 20-23极大风对应的风速
     **/
    @JsonProperty("wind_speed_20_23")
    @TableField("wind_speed_20_23")
    private String windSpeed2023;
    /**
     * 20-23极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_20_23_Qc2")
    @TableField("wind_speed_20_23_qc2")
    private Integer windSpeed2023Qc2;
    /**
     * 23-02极大风对应的风向
     **/
    @JsonProperty("wind_dir_23_02")
    @TableField("wind_dir_23_02")
    private String windDir2302;
    /**
     * 23-02极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_23_02_Qc2")
    @TableField("wind_dir_23_02_qc2")
    private Integer windDir2302Qc2;
    /**
     * 23-02极大风对应的风速
     **/
    @JsonProperty("wind_speed_23_02")
    @TableField("wind_speed_23_02")
    private String windSpeed2302;
    /**
     * 23-02极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_23_02_Qc2")
    @TableField("wind_speed_23_02_qc2")
    private Integer windSpeed2302Qc2;
    /**
     * 02-05极大风对应的风向
     **/
    @JsonProperty("wind_dir_02_05")
    @TableField("wind_dir_02_05")
    private String windDir0205;
    /**
     * 02-05极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_02_05_Qc2")
    @TableField("wind_dir_02_05_qc2")
    private Integer windDir0205Qc2;
    /**
     * 02-05极大风对应的风速
     **/
    @JsonProperty("wind_speed_02_05")
    @TableField("wind_speed_02_05")
    private String windSpeed0205;
    /**
     * 02-05极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_02_05_Qc2")
    @TableField("wind_speed_02_05_qc2")
    private Integer windSpeed0205Qc2;
    /**
     * 05-08极大风对应的风向
     **/
    @JsonProperty("wind_dir_05_08")
    @TableField("wind_dir_05_08")
    private String windDir0508;
    /**
     * 05-08极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_05_08_Qc2")
    @TableField("wind_dir_05_08_qc2")
    private Integer windDir0508Qc2;
    /**
     * 05-08极大风对应的风速
     **/
    @JsonProperty("wind_speed_05_08")
    @TableField("wind_speed_05_08")
    private String windSpeed0508;
    /**
     * 05-08极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_05_08_Qc2")
    @TableField("wind_speed_05_08_qc2")
    private Integer windSpeed0508Qc2;
    /**
     * 08-11极大风对应的风向
     **/
    @JsonProperty("wind_dir_08_11")
    @TableField("wind_dir_08_11")
    private String windDir0811;
    /**
     * 08-11极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_08_11_Qc2")
    @TableField("wind_dir_08_11_qc2")
    private Integer windDir0811Qc2;
    /**
     * 08-11极大风对应的风速
     **/
    @JsonProperty("wind_speed_08_11")
    @TableField("wind_speed_08_11")
    private String windSpeed0811;
    /**
     * 08-11极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_08_11_Qc2")
    @TableField("wind_speed_08_11_qc2")
    private Integer windSpeed0811Qc2;
    /**
     * 11-14极大风对应的风向
     **/
    @JsonProperty("wind_dir_11_14")
    @TableField("wind_dir_11_14")
    private String windDir1114;
    /**
     * 11-14极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_11_14_Qc2")
    @TableField("wind_dir_11_14_qc2")
    private Integer windDir1114Qc2;
    /**
     * 11-14极大风对应的风速
     **/
    @JsonProperty("wind_speed_11_14")
    @TableField("wind_speed_11_14")
    private String windSpeed1114;
    /**
     * 11-14极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_11_14_Qc2")
    @TableField("wind_speed_11_14_qc2")
    private Integer windSpeed1114Qc2;
    /**
     * 14-17极大风对应的风向
     **/
    @JsonProperty("wind_dir_14_17")
    @TableField("wind_dir_14_17")
    private String windDir1417;
    /**
     * 14-17极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_14_17_Qc2")
    @TableField("wind_dir_14_17_qc2")
    private Integer windDir1417Qc2;
    /**
     * 14-17极大风对应的风速
     **/
    @JsonProperty("wind_speed_14_17")
    @TableField("wind_speed_14_17")
    private String windSpeed1417;
    /**
     * 14-17极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_14_17_Qc2")
    @TableField("wind_speed_14_17_qc2")
    private Integer windSpeed1417Qc2;
    /**
     * 17-20极大风对应的风向
     **/
    @JsonProperty("wind_dir_17_20")
    @TableField("wind_dir_17_20")
    private String windDir1720;
    /**
     * 17-20极大风对应的风向质控符
     **/
    @JsonProperty("wind_dir_17_20_Qc2")
    @TableField("wind_dir_17_20_qc2")
    private Integer windDir1720Qc2;
    /**
     * 17-20极大风对应的风速
     **/
    @JsonProperty("wind_speed_17_20")
    @TableField("wind_speed_17_20")
    private String windSpeed1720;
    /**
     * 17-20极大风对应的风速质控符
     **/
    @JsonProperty("wind_speed_17_20_Qc2")
    @TableField("wind_speed_17_20_qc2")
    private Integer windSpeed1720Qc2;
    /**
     * 最大风的风向
     **/
    @JsonProperty("max_wind_dir")
    @TableField("max_wind_dir")
    private String maxWindDir;
    /**
     * 最大风的风向质控符
     **/
    @JsonProperty("max_wind_dir_Qc2")
    @TableField("max_wind_dir_qc2")
    private Integer maxWindDirQc2;
    /**
     * 最大风的风速
     **/
    @JsonProperty("max_wind_speed")
    @TableField("max_wind_speed")
    private String maxWindSpeed;
    /**
     * 最大风的风速质控符
     **/
    @JsonProperty("max_wind_speed_Qc2")
    @TableField("max_wind_speed_qc2")
    private Integer maxWindSpeedQc2;
    /**
     * 最大风的出现的时间
     **/
    @JsonProperty("max_wind_time")
    @TableField("max_wind_time")
    private String maxWindTime;
    /**
     * 最大风的出现的时间质控符
     **/
    @JsonProperty("max_wind_time_Qc2")
    @TableField("max_wind_time_qc2")
    private Integer maxWindTimeQc2;
    /**
     * 极大风的风向
     **/
    @JsonProperty("extreme_wind_dir")
    @TableField("extreme_wind_dir")
    private String extremeWindDir;
    /**
     * 极大风的风向质控符
     **/
    @JsonProperty("extreme_wind_dir_Qc2")
    @TableField("extreme_wind_dir_qc2")
    private Integer extremeWindDirQc2;
    /**
     * 极大风的风速
     **/
    @JsonProperty("extreme_wind_speed")
    @TableField("extreme_wind_speed")
    private String extremeWindSpeed;
    /**
     * 极大风的风速质控符
     **/
    @JsonProperty("extreme_wind_speed_Qc2")
    @TableField("extreme_wind_speed_qc2")
    private Integer extremeWindSpeedQc2;
    /**
     * 极大风的出现的时间
     **/
    @JsonProperty("extreme_wind_time")
    @TableField("extreme_wind_time")
    private String extremeWindTime;
    /**
     * 极大风的出现的时间质控符
     **/
    @JsonProperty("extreme_wind_time_Qc2")
    @TableField("extreme_wind_time_qc2")
    private Integer extremeWindTimeQc2;
    /**
     * 大于17m/s风速出现的起止时间1
     **/
    @JsonProperty("wind_speeds_gt17_1")
    @TableField("wind_speeds_gt17_1")
    private String windSpeedsGt171;
    /**
     * 大于17m/s风速出现的起止时间1质控符
     **/
    @JsonProperty("wind_speeds_gt17_1_Qc2")
    @TableField("wind_speeds_gt17_1_qc2")
    private Integer windSpeedsGt171Qc2;
    /**
     * 大于17m/s风速出现的起止时间2
     **/
    @JsonProperty("wind_speeds_gt17_2")
    @TableField("wind_speeds_gt17_2")
    private String windSpeedsGt172;
    /**
     * 大于17m/s风速出现的起止时间2质控符
     **/
    @JsonProperty("wind_speeds_gt17_2_Qc2")
    @TableField("wind_speeds_gt17_2_qc2")
    private Integer windSpeedsGt172Qc2;
    /**
     * 大于17m/s风速出现的起止时间3
     **/
    @JsonProperty("wind_speeds_gt17_3")
    @TableField("wind_speeds_gt17_3")
    private String windSpeedsGt173;
    /**
     * 大于17m/s风速出现的起止时间3质控符
     **/
    @JsonProperty("wind_speeds_gt17_3_Qc2")
    @TableField("wind_speeds_gt17_3_qc2")
    private Integer windSpeedsGt173Qc2;
    /**
     * 大于17m/s风速出现的起止时间4
     **/
    @JsonProperty("wind_speeds_gt17_4")
    @TableField("wind_speeds_gt17_4")
    private String windSpeedsGt174;
    /**
     * 大于17m/s风速出现的起止时间4质控符
     **/
    @JsonProperty("wind_speeds_gt17_4_Qc2")
    @TableField("wind_speeds_gt17_4_qc2")
    private Integer windSpeedsGt174Qc2;
    /**
     * 大于17m/s风速出现的起止时间5
     **/
    @JsonProperty("wind_speeds_gt17_5")
    @TableField("wind_speeds_gt17_5")
    private String windSpeedsGt175;
    /**
     * 大于17m/s风速出现的起止时间5质控符
     **/
    @JsonProperty("wind_speeds_gt17_5_Qc2")
    @TableField("wind_speeds_gt17_5_qc2")
    private Integer windSpeedsGt175Qc2;
    /**
     * 大于17m/s风速出现的起止时间6
     **/
    @JsonProperty("wind_speeds_gt17_6")
    @TableField("wind_speeds_gt17_6")
    private String windSpeedsGt176;
    /**
     * 大于17m/s风速出现的起止时间6质控符
     **/
    @JsonProperty("wind_speeds_gt17_6_Qc2")
    @TableField("wind_speeds_gt17_6_qc2")
    private Integer windSpeedsGt176Qc2;
    /**
     * 大于17m/s风速出现的起止时间7
     **/
    @JsonProperty("wind_speeds_gt17_7")
    @TableField("wind_speeds_gt17_7")
    private String windSpeedsGt177;
    /**
     * 大于17m/s风速出现的起止时间7质控符
     **/
    @JsonProperty("wind_speeds_gt17_7_Qc2")
    @TableField("wind_speeds_gt17_7_qc2")
    private Integer windSpeedsGt177Qc2;
    /**
     * 大于17m/s风速出现的起止时间8
     **/
    @JsonProperty("wind_speeds_gt17_8")
    @TableField("wind_speeds_gt17_8")
    private String windSpeedsGt178;
    /**
     * 大于17m/s风速出现的起止时间8质控符
     **/
    @JsonProperty("wind_speeds_gt17_8_Qc2")
    @TableField("wind_speeds_gt17_8_qc2")
    private Integer windSpeedsGt178Qc2;
    /**
     * 大于17m/s风速出现的起止时间9
     **/
    @JsonProperty("wind_speeds_gt17_9")
    @TableField("wind_speeds_gt17_9")
    private String windSpeedsGt179;
    /**
     * 大于17m/s风速出现的起止时间9质控符
     **/
    @JsonProperty("wind_speeds_gt17_9_Qc2")
    @TableField("wind_speeds_gt17_9_qc2")
    private Integer windSpeedsGt179Qc2;
    /**
     * 大于17m/s风速出现的起止时间10
     **/
    @JsonProperty("wind_speeds_gt17_10")
    @TableField("wind_speeds_gt17_10")
    private String windSpeedsGt1710;
    /**
     * 大于17m/s风速出现的起止时间10质控符
     **/
    @JsonProperty("wind_speeds_gt17_10_Qc2")
    @TableField("wind_speeds_gt17_10_qc2")
    private Integer windSpeedsGt1710Qc2;
    /**
     * 大于17m/s风速出现的起止时间11
     **/
    @JsonProperty("wind_speeds_gt17_11")
    @TableField("wind_speeds_gt17_11")
    private String windSpeedsGt1711;
    /**
     * 大于17m/s风速出现的起止时间11质控符
     **/
    @JsonProperty("wind_speeds_gt17_11_Qc2")
    @TableField("wind_speeds_gt17_11_qc2")
    private Integer windSpeedsGt1711Qc2;
    /**
     * 大于17m/s风速出现的起止时间12
     **/
    @JsonProperty("wind_speeds_gt17_12")
    @TableField("wind_speeds_gt17_12")
    private String windSpeedsGt1712;
    /**
     * 大于17m/s风速出现的起止时间12质控符
     **/
    @JsonProperty("wind_speeds_gt17_12_Qc2")
    @TableField("wind_speeds_gt17_12_qc2")
    private Integer windSpeedsGt1712Qc2;
    /**
     * 大于17m/s风速出现的起止时间13
     **/
    @JsonProperty("wind_speeds_gt17_13")
    @TableField("wind_speeds_gt17_13")
    private String windSpeedsGt1713;
    /**
     * 大于17m/s风速出现的起止时间13质控符
     **/
    @JsonProperty("wind_speeds_gt17_13_Qc2")
    @TableField("wind_speeds_gt17_13_qc2")
    private Integer windSpeedsGt1713Qc2;
    /**
     * 大于17m/s风速出现的起止时间1
     **/
    @JsonProperty("wind_speeds_gt17_14")
    @TableField("wind_speeds_gt17_14")
    private String windSpeedsGt1714;
    /**
     * 大于17m/s风速出现的起止时间14质控符
     **/
    @JsonProperty("wind_speeds_gt17_14_Qc2")
    @TableField("wind_speeds_gt17_14_qc2")
    private Integer windSpeedsGt1714Qc2;
    /**
     * 大于17m/s风速出现的起止时间15
     **/
    @JsonProperty("wind_speeds_gt17_15")
    @TableField("wind_speeds_gt17_15")
    private String windSpeedsGt1715;
    /**
     * 大于17m/s风速出现的起止时间15质控符
     **/
    @JsonProperty("wind_speeds_gt17_15_Qc2")
    @TableField("wind_speeds_gt17_15_qc2")
    private Integer windSpeedsGt1715Qc2;
    /**
     * 大于17m/s风速出现的起止时间16
     **/
    @JsonProperty("wind_speeds_gt17_16")
    @TableField("wind_speeds_gt17_16")
    private String windSpeedsGt1716;
    /**
     * 大于17m/s风速出现的起止时间16质控符
     **/
    @JsonProperty("wind_speeds_gt17_16_Qc2")
    @TableField("wind_speeds_gt17_16_qc2")
    private Integer windSpeedsGt1716Qc2;
    /**
     * 大于17m/s风速出现的起止时间17
     **/
    @JsonProperty("wind_speeds_gt17_17")
    @TableField("wind_speeds_gt17_17")
    private String windSpeedsGt1717;
    /**
     * 大于17m/s风速出现的起止时间17质控符
     **/
    @JsonProperty("wind_speeds_gt17_17_Qc2")
    @TableField("wind_speeds_gt17_17_qc2")
    private Integer windSpeedsGt1717Qc2;
    /**
     * 大于17m/s风速出现的起止时间18
     **/
    @JsonProperty("wind_speeds_gt17_18")
    @TableField("wind_speeds_gt17_18")
    private String windSpeedsGt1718;
    /**
     * 大于17m/s风速出现的起止时间18质控符
     **/
    @JsonProperty("wind_speeds_gt17_18_Qc2")
    @TableField("wind_speeds_gt17_18_qc2")
    private Integer windSpeedsGt1718Qc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



