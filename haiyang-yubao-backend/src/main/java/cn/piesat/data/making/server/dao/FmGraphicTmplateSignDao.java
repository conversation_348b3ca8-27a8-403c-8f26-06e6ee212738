package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmGraphicTemplateTools;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import cn.piesat.data.making.server.entity.FmGraphicTmplateSign;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-10-09 16:03:17
 */
public interface FmGraphicTmplateSignDao extends BaseMapper<FmGraphicTmplateSign> {
    @Select("SELECT * FROM fm_graphic_tmplate_sign WHERE template_id = #{templateId}")
    List<FmGraphicTmplateSign> selectByTemplateId(Long templateId);
    @Delete("DELETE  FROM fm_graphic_tmplate_sign where template_id = #{mainId}")
    void deleteByTemplateId(Long templateId);
    @Select("SELECT count(*) FROM fm_graphic_tmplate_sign WHERE template_id = #{templateId}")
    int countByTemplateId(Long templateId);
}
