package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastProductRecordDTO;
import cn.piesat.data.making.server.entity.ForecastProductRecord;
import cn.piesat.data.making.server.vo.ForecastProductRecordVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastProductRecordMapper {

    ForecastProductRecordMapper INSTANCE = Mappers.getMapper(ForecastProductRecordMapper.class);

    /**
     * entity-->vo
     */
    ForecastProductRecordVO entityToVo(ForecastProductRecord entity);

    /**
     * dto-->entity
     */
    ForecastProductRecord dtoToEntity(ForecastProductRecordDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastProductRecordVO> entityListToVoList(List<ForecastProductRecord> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastProductRecord> dtoListToEntityList(List<ForecastProductRecordDTO> dtoList);
}
