package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 预报产品模板表实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("fm_forecast_product_template_b")
public class ForecastProductTemplate implements Serializable {

    private static final long serialVersionUID = 384351344703923756L;

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 编码
     **/
    @TableField("code")
    private String code;
    /**
     * 名称
     **/
    @TableField("name")
    private String name;
    /**
     * 状态
     **/
    @TableField("status")
    private Boolean status;
    /**
     * 排序
     **/
    @TableField("sort")
    private Integer sort;
    /**
     * 文件类型
     **/
    @TableField("file_type")
    private String fileType;
    /**
     * 关联的模板编码
     **/
    @TableField("relation_template_code")
    private String relationTemplateCode;
    /**
     * 文件内容
     **/
    @TableField("file_content")
    private String fileContent;
    /**
     * 文件地址
     **/
    @TableField("file_url")
    private String fileUrl;
    /**
     * 创建人id
     **/
    @TableField(value = "create_user_id", fill = FieldFill.INSERT)
    private Long createUserId;
    /**
     * 创建人
     **/
    @TableField(value = "create_user", fill = FieldFill.INSERT)
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;
    /**
     * 更新人id
     **/
    @TableField(value = "update_user_id", fill = FieldFill.INSERT_UPDATE)
    private Long updateUserId;
    /**
     * 更新人
     **/
    @TableField(value = "update_user", fill = FieldFill.INSERT_UPDATE)
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
    /**
     * 数管-数据产品id
     **/
    @TableField("product_id")
    private Long productId;
}



