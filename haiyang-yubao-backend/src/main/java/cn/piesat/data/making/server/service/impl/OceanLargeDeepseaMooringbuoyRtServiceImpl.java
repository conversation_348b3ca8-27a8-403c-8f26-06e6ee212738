package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanLargeDeepseaMooringbuoyRtDao;
import cn.piesat.data.making.server.entity.OceanLargeDeepseaMooringbuoyRt;
import cn.piesat.data.making.server.service.OceanLargeDeepseaMooringbuoyRtService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 深海大型锚系浮标实时数据服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanLargeDeepseaMooringbuoyRtServiceImpl extends ServiceImpl<OceanLargeDeepseaMooringbuoyRtDao, OceanLargeDeepseaMooringbuoyRt>
        implements OceanLargeDeepseaMooringbuoyRtService {
}





