package cn.piesat.data.making.server.enums;

public enum WaveLevelEnum {
    微(0, 0.1),
    小(0.1, 0.5),
    轻(0.5, 1.25),
    中(1.25, 2.5),
    大(2.5, 4.0),
    巨(4.0, 6.0),
    狂(6.0, 9.0),
    狂涛(9.0, 14.0),
    怒涛(14.0, Double.MAX_VALUE);

    private double minHeight;
    private double maxHeight;

    WaveLevelEnum(double minHeight, double maxHeight) {
        this.minHeight = minHeight;
        this.maxHeight = maxHeight;
    }

    public boolean includes(double height) {
        return height >= minHeight && height < maxHeight;
    }

    public static String classifyWave(double height) {
        for (WaveLevelEnum level : WaveLevelEnum.values()) {
            if (level.includes(height)) {
                return level.name();
            }
        }
        return "未知级别";
    }

    public static void main(String[] args) {
        double waveHeight = 3.5; // 测试不同的海浪高度
        String classification = classifyWave(waveHeight);
        System.out.println("海浪级别: " + classification);
    }
}
