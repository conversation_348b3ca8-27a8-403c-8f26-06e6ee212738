package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastRecordDetailDTO;
import cn.piesat.data.making.server.entity.ForecastRecordDetail;
import cn.piesat.data.making.server.vo.ForecastRecordDetailVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper(componentModel = "spring")
public interface ForecastRecordDetailMapper {

    ForecastRecordDetailMapper INSTANCE = Mappers.getMapper(ForecastRecordDetailMapper.class);

    /**
     * entity-->vo
     */
    ForecastRecordDetailVO entityToVo(ForecastRecordDetail entity);

    /**
     * dto-->entity
     */
    ForecastRecordDetail dtoToEntity(ForecastRecordDetailDTO dto);

    /**
     * entityList-->voList
     */
    List<ForecastRecordDetailVO> entityListToVoList(List<ForecastRecordDetail> list);

    /**
     * dtoList-->entityList
     */
    List<ForecastRecordDetail> dtoListToEntityList(List<ForecastRecordDetailDTO> dtoList);
}
