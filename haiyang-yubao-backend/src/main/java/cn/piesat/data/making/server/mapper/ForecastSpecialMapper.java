package cn.piesat.data.making.server.mapper;

import cn.piesat.data.making.server.dto.ForecastSpecialDTO;
import cn.piesat.data.making.server.entity.ForecastSpecial;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface ForecastSpecialMapper {

    ForecastSpecialMapper INSTANCE = Mappers.getMapper(ForecastSpecialMapper.class);

    @Mappings({
            @Mapping(target = "createTime",expression = "java(new java.util.Date())"),
            @Mapping(target = "modifyTime",expression = "java(new java.util.Date())"),
            @Mapping(target = "status",expression = "java(0)"),
    })
    ForecastSpecial toForecastSpecial(ForecastSpecialDTO dto);

    @Mappings({
            @Mapping(target = "modifyTime",expression = "java(new java.util.Date())")
    })
    ForecastSpecial toForecastSpecialModify(ForecastSpecialDTO dto);

    ForecastSpecialDTO toForecastSpecialDTO(ForecastSpecial entity);

}
