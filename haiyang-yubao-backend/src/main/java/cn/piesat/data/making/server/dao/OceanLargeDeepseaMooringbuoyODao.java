package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.OceanLargeDeepseaMooringbuoyO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 深海大型锚系浮标原始数据表数据库访问层
 *
 * <AUTHOR>
 */
public interface OceanLargeDeepseaMooringbuoyODao extends BaseMapper<OceanLargeDeepseaMooringbuoyO> {

    @Select({"<script>",
            "select * from ocean_large_deepsea_mooringbuoy_o where monitoringtime between #{startTime} and #{endTime} " +
                    "and buoyinfo_id in " +
                    "<foreach collection='stationNumList' item='stationNum' open='(' separator=',' close=')'> " +
                    "#{stationNum}" +
                    "</foreach>",
            "</script>"})
    List<OceanLargeDeepseaMooringbuoyO> getByStationNumListAndDateRange(Date startTime, Date endTime, List<String> stationNumList);

    @Select("SELECT MAX(createtime) FROM ocean_large_deepsea_mooringbuoy_o where buoyinfo_id = #{buoyInfoId}")
    LocalDateTime getMaxCreateTime(String buoyInfoId);
}
