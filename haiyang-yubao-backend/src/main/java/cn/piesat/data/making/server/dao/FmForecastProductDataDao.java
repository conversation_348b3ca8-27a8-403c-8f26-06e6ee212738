package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.FmForecastProductData;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.Date;

/**
 * 数据库访问层
 *
 * <AUTHOR>
 * @date 2024-09-27 10:27:54
 */
public interface FmForecastProductDataDao extends BaseMapper<FmForecastProductData> {

    @Select("select start_time from fm_forecast_product_data where data_source = #{dataSource} order by start_time desc limit 1")
    Date getLastForecastStartTime(String dataSource);
}
