package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@Accessors(chain = true)
@TableName("fm_publish_forecast")
public class FmPublishForecast {

    /**
     * id
     **/
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("time_range")
    private String timeRange;

    @TableField("wave")
    private String wave;

    @TableField("wave_cycle")
    private String waveCycle;

    @TableField("temperature")
    private String temperature;

    @TableField("swimming")
    private String swimming;

    @TableField("hint")
    private String hint;

    @TableField("level")
    private String level;

    @TableField("station_id")
    private Long stationId;

    @TableField("station_name")
    private String stationName;

    @TableField("product_type")
    private String productType;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("time_publish")
    private Date timePublish;

    @TableField("area_type_code")
    private String areaTypeCode;

    public Long getId() {
        return id;
    }

    public FmPublishForecast setId(Long id) {
        this.id = id;
        return this;
    }

    public String getTimeRange() {
        return timeRange;
    }

    public FmPublishForecast setTimeRange(String timeRange) {
        this.timeRange = timeRange;
        return this;
    }

    public String getWave() {
        return wave;
    }

    public FmPublishForecast setWave(String wave) {
        this.wave = wave;
        return this;
    }

    public String getWaveCycle() {
        return waveCycle;
    }

    public FmPublishForecast setWaveCycle(String waveCycle) {
        this.waveCycle = waveCycle;
        return this;
    }

    public String getTemperature() {
        return temperature;
    }

    public FmPublishForecast setTemperature(String temperature) {
        this.temperature = temperature;
        return this;
    }

    public String getSwimming() {
        return swimming;
    }

    public FmPublishForecast setSwimming(String swimming) {
        this.swimming = swimming;
        return this;
    }

    public String getHint() {
        return hint;
    }

    public FmPublishForecast setHint(String hint) {
        this.hint = hint;
        return this;
    }

    public String getLevel() {
        return level;
    }

    public FmPublishForecast setLevel(String level) {
        this.level = level;
        return this;
    }

    public Long getStationId() {
        return stationId;
    }

    public FmPublishForecast setStationId(Long stationId) {
        this.stationId = stationId;
        return this;
    }

    public String getStationName() {
        return stationName;
    }

    public FmPublishForecast setStationName(String stationName) {
        this.stationName = stationName;
        return this;
    }

    public String getProductType() {
        return productType;
    }

    public FmPublishForecast setProductType(String productType) {
        this.productType = productType;
        return this;
    }



    public String getAreaTypeCode() {
        return areaTypeCode;
    }

    public FmPublishForecast setAreaTypeCode(String areaTypeCode) {
        this.areaTypeCode = areaTypeCode;
        return this;
    }

    public Date getTimePublish() {
        return timePublish;
    }

    public FmPublishForecast setTimePublish(Date timePublish) {
        this.timePublish = timePublish;
        return this;
    }
}
