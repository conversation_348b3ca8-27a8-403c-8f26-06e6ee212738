package cn.piesat.data.making.server.entity;


import java.io.Serializable;
import java.util.Date;

//import org.hibernate.annotations.GenericGenerator;
import cn.piesat.common.utils.Constant;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-09-04 17:11:56
 */
@Data
@Accessors(chain = true)
@TableName("scheduling_table")
public class SchedulingTable implements Serializable {

    private static final long serialVersionUID = 350568372051828363L;


    @TableId(type = IdType.ASSIGN_ID)
    private Long id;
    @TableField("scheduling_date")
    private Date schedulingDate;
    @TableField("user_name")
    private String userName;
    @TableField("user_id")
    private Long userId;
    @TableField("day")
    private Integer day;
    @TableField("scheduling_main_id")
    private Long schedulingMainId;
    @TableField("sign_date")
    private Date signDate;
    @TableField("sign_user_name")
    private String signUserName;
    @TableField("sign_user_id")
    private Long signUserId;

    public Date getSignDate() {
        return signDate;
    }

    public void setSignDate(Date signDate) {
        this.signDate = signDate;
    }

    public String getSignUserName() {
        return signUserName;
    }

    public void setSignUserName(String signUserName) {
        this.signUserName = signUserName;
    }

    public Long getSignUserId() {
        return signUserId;
    }

    public void setSignUserId(Long signUserId) {
        this.signUserId = signUserId;
    }

    public Long getSchedulingMainId() {
        return schedulingMainId;
    }

    public void setSchedulingMainId(Long schedulingMainId) {
        this.schedulingMainId = schedulingMainId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getSchedulingDate() {
        return schedulingDate;
    }

    public void setSchedulingDate(Date schedulingDate) {
        this.schedulingDate = schedulingDate;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getDay() {
        return day;
    }

    public void setDay(Integer day) {
        this.day = day;
    }

}
