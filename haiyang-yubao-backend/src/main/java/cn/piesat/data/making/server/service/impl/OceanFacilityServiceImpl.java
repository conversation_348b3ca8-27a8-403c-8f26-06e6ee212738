package cn.piesat.data.making.server.service.impl;

import cn.piesat.data.making.server.dao.OceanFacilityDao;
import cn.piesat.data.making.server.entity.OceanFacility;
import cn.piesat.data.making.server.service.OceanFacilityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 海洋设施表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OceanFacilityServiceImpl extends ServiceImpl<OceanFacilityDao, OceanFacility>
        implements OceanFacilityService {

    @Resource
    private OceanFacilityDao oceanFacilityDao;
}





