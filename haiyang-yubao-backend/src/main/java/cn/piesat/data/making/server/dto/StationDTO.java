package cn.piesat.data.making.server.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * 站点表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class StationDTO implements Serializable {

    private static final long serialVersionUID = -65368690249266193L;

    public interface Save {
    }

    /**
     * id
     **/
    private Long id;
    /**
     * 编码
     **/
    private String code;
    /**
     * 名称
     **/
    @NotBlank(message = "名称不能为空", groups = {Save.class})
    private String name;
    /**
     * 站点类型编码
     **/
    @NotBlank(message = "站点类型编码不能为空", groups = {Save.class})
    private String stationTypeCode;
    /**
     * 行政区编码
     **/
//    @NotBlank(message = "行政区编码不能为空", groups = {Save.class})
    private String regionCode;
    /**
     * 位置
     **/
    private String locationGeo;
    /**
     * 位置
     **/
//    @NotBlank(message = "位置不能为空", groups = {Save.class})
    private String locationJson;
    /**
     * 创建人id
     **/
    private Long createUserId;
    /**
     * 创建人
     **/
    private String createUser;
    /**
     * 创建时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人id
     **/
    private Long updateUserId;
    /**
     * 更新人
     **/
    private String updateUser;
    /**
     * 更新时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 85基面-黄色警戒值
     **/
    private Integer datumYellowWarn;
    /**
     * 85基面-蓝色警戒值
     **/
    private Integer datumBlueWarn;
    /**
     * 85基面-红色警戒值
     **/
    private Integer datumRedWarn;
    /**
     * 85基面-橙色警戒值
     **/
    private Integer datumOrangeWarn;
    /**
     * 潮汐表基面-黄色警戒值
     **/
    private Integer tideDatumYellowWarn;
    /**
     * 潮汐表基面-蓝色警戒值
     **/
    private Integer tideDatumBlueWarn;
    /**
     * 潮汐表基面-红色警戒值
     **/
    private Integer tideDatumRedWarn;
    /**
     * 潮汐表基面-橙色警戒值
     **/
    private Integer tideDatumOrangeWarn;
    /**
     * 水尺零点-黄色警戒值
     **/
    private Integer gaugeZeroYellowWarn;
    /**
     * 水尺零点-蓝色警戒值
     **/
    private Integer gaugeZeroBlueWarn;
    /**
     * 水尺零点-红色警戒值
     **/
    private Integer gaugeZeroRedWarn;
    /**
     * 水尺零点-橙色警戒值
     **/
    private Integer gaugeZeroOrangeWarn;
    /**
     * 类型：潮位tide 实况观测liveObservation 3米浮标站3m 10米浮标站10m 波浪谱浮标站waveSpectrum
     **/
    private String type;
    /**
     * 关联站点
     **/
    private String relationStation;
    /**
     * 是否启用
     **/
    private Boolean enable;
    /**
     * 是否是天文大潮站点
     **/
    private Boolean isAstronomicalTide;
}



