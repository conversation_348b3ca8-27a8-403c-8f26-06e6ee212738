package cn.piesat.data.making.server.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

/**
 * 海洋站数据
 *
 * <AUTHOR>
 */
@Data
public class OceanStationDataInfo {
    private Long id;
    private String stationCode;
    private String stationName;
    private String stationCNname;
    private Double lon;
    private Double lat;
    private List<String> time;
    @JsonProperty("Windspeed")
    private List<Double> windspeed;
    @JsonProperty("Winddir")
    private List<Double> winddir;
    @JsonProperty("SeaTemperature")
    private List<Double> seaTemperature;
    @JsonProperty("WindwaveHeight")
    private List<Double> WindwaveHeight;
    @JsonProperty("WindWavePeriod")
    private List<Double> WindWavePeriod;
    @JsonProperty("Statusbak")
    private Integer Statusbak;
}
