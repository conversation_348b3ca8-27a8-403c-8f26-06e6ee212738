package cn.piesat.data.making.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.piesat.common.utils.JsonUtil;
import cn.piesat.data.making.server.constant.CommonConstant;
import cn.piesat.data.making.server.dao.ForecastProductTemplateDao;
import cn.piesat.data.making.server.dto.ForecastProductTemplateDTO;
import cn.piesat.data.making.server.entity.ForecastProductTemplate;
import cn.piesat.data.making.server.enums.FileType;
import cn.piesat.data.making.server.mapper.ForecastProductTemplateMapper;
import cn.piesat.data.making.server.model.FileInfo;
import cn.piesat.data.making.server.service.ForecastProductTemplateService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.vo.ForecastProductTemplateVO;
import cn.piesat.sys.admin.core.dto.ProductInfoDTO;
import cn.piesat.sys.admin.starter.feign.FDataProductBaseService;
import cn.piesat.webconfig.exception.BusinessException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 预报产品模板表服务实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@Slf4j
public class ForecastProductTemplateServiceImpl extends ServiceImpl<ForecastProductTemplateDao, ForecastProductTemplate>
        implements ForecastProductTemplateService {

    @Value("${piesat.base-output-path}")
    private String baseOutputPath;
    @Resource
    private FDataProductBaseService fDataProductBaseService;

    @Override
    public List<ForecastProductTemplateVO> getList(ForecastProductTemplateDTO dto) {
        List<ForecastProductTemplate> list = this.list(createQueryWrapper(dto));
        return ForecastProductTemplateMapper.INSTANCE.entityListToVoList(list);
    }

    @Override
    public ForecastProductTemplateVO getInfoById(Long id) {
        return ForecastProductTemplateMapper.INSTANCE.entityToVo(this.getById(id));
    }

    @Override
    public Long save(ForecastProductTemplateDTO dto) {
        LambdaQueryWrapper<ForecastProductTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ForecastProductTemplate::getCode, dto.getCode());
        List<ForecastProductTemplate> existList = this.list(queryWrapper);
        if (dto.getId() == null && !CollectionUtils.isEmpty(existList)) {
            throw new BusinessException("该编码已经存在！");
        }
        //同步保存数据产品
        Long productId = this.saveProductInfo(dto);

        ForecastProductTemplate entity = ForecastProductTemplateMapper.INSTANCE.dtoToEntity(dto);
        if (dto.getId() == null) {
            entity.setProductId(productId);
            this.save(entity);
        } else {
            //更新文件内容
            if (FileType.TXT.getValue().equals(entity.getFileType()) || FileType.XML.getValue().equals(entity.getFileType())) {
                FileInfo fileInfo = new FileInfo();
                fileInfo.setPath(entity.getFileUrl());
                fileInfo.setContent(entity.getFileContent());
                FileUtil.updateFile(fileInfo);
            }
            List<ForecastProductTemplate> list = existList.stream().filter(obj -> !obj.getId().equals(dto.getId())).collect(Collectors.toList());
            if (dto.getId() != null && !CollectionUtils.isEmpty(list)) {
                throw new BusinessException("该编码已经存在！");
            }
            this.updateById(entity);
        }
        return entity.getId();
    }

    @Override
    public String uploadFile(MultipartFile multipartFile) {
        Date date = new Date();
        int year = DateUtil.year(date);
        String day = DateUtil.format(date, "yyyyMMdd");
        String basePath = String.format(CommonConstant.FORECAST_TEMPLATE_PATH, baseOutputPath, year, day);
        return FileUtil.uploadFile(basePath, multipartFile);
    }

    @Override
    public void downloadFile(Long id, HttpServletResponse response) {
        ForecastProductTemplate template = this.getById(id);
        if (template == null) {
            throw new BusinessException("产品模板不存在！");
        }
        FileUtil.downloadFile(template.getFileUrl(), template.getName(), response);
    }

    @Override
    public void deleteById(Long id) {
        this.removeById(id);
    }

    /**
     * 同步保存数据产品
     **/
    private Long saveProductInfo(ForecastProductTemplateDTO template) {
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setId(template.getProductId());
        productInfo.setNamespaceId(1836710633487773698L);
        productInfo.setProductId(template.getCode());
        productInfo.setProductName(template.getName());
        productInfo.setDataFormat(template.getFileType().split("\\.")[1].toUpperCase());
        productInfo.setDataSource("PRECESS");
        Map<String, Object> properties = new HashMap<>();
        properties.put("big_type", "F");
        properties.put("son_type", "FDRW");
        properties.put("product_level", "S4");
        properties.put("ext_code", "00000");
        properties.put("time_split", "STP");
        productInfo.setProperties(properties);
        log.debug("同步保存数据产品:{}", JsonUtil.object2Json(productInfo));
        if (productInfo.getId() == null) {
            productInfo = fDataProductBaseService.save(productInfo);
        } else {
            fDataProductBaseService.update(productInfo);
        }
        return productInfo.getId();
    }

    private LambdaQueryWrapper<ForecastProductTemplate> createQueryWrapper(ForecastProductTemplateDTO dto) {
        LambdaQueryWrapper<ForecastProductTemplate> queryWrapper = new LambdaQueryWrapper<>();
        if (dto.getStatus() != null) {
            queryWrapper.eq(ForecastProductTemplate::getStatus, dto.getStatus());
        }
        if (StringUtils.isNotBlank(dto.getFileType())) {
            queryWrapper.eq(ForecastProductTemplate::getFileType, dto.getFileType());
        }
        return queryWrapper.orderByAsc(ForecastProductTemplate::getSort, ForecastProductTemplate::getCreateTime);
    }
}





