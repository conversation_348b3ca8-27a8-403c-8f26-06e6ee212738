package cn.piesat.data.making.server.service;

import cn.piesat.data.making.server.vo.FmPublicProductTemplateVO;

import java.util.List;

public interface FmPublicProductTemplateService {

    FmPublicProductTemplateVO getById(Long id);

    FmPublicProductTemplateVO getByTemplateCode(String code);

    void save(FmPublicProductTemplateVO vo);

    void update(FmPublicProductTemplateVO vo);

    List<FmPublicProductTemplateVO> getListByParam(FmPublicProductTemplateVO vo);
}
