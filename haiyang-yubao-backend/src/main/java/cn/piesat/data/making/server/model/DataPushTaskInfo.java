package cn.piesat.data.making.server.model;

import lombok.Data;

import java.io.Serializable;

/**
 * 推送任务信息
 *
 * <AUTHOR>
 */
@Data
public class DataPushTaskInfo implements Serializable {
    private String id;
    private String taskName;
    private Boolean status;
    private Integer taskType;
    private String unitType;
    private String groupId;
    private String callbackId;
    private String createTime;
    private String updateTime;
    private String pushDataSourceId;
    private String triggerCron;
    private String strategyName;
    private String dataTimeFunction;
    private String pushUnit;

    @Data
    public static class PushProductInfo implements Serializable {
        private String id;
        private String basicProductId;
        private String createTime;
        private String taskId;
        private String productId;
        private String renameTmp;
        private String subPathTemplate;
        private String productName;
        private String dataFormat;
    }
}
