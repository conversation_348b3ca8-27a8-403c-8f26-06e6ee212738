package cn.piesat.data.making.server.dao;

import cn.piesat.data.making.server.entity.ForecastProductRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 预报产品记录表表数据库访问层
 *
 * <AUTHOR>
 */
public interface ForecastProductRecordDao extends BaseMapper<ForecastProductRecord> {

    @Select("select push_task_id pushTaskId, max(update_time) updateTime from fm_forecast_product_record_b where push_task_id is not null group by " +
            "push_task_id")
    List<ForecastProductRecord> getPushTaskInfoList();
}
