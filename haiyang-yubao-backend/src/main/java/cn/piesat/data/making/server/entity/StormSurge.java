package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 风暴潮表实体类
 *
 * <AUTHOR>
 */
@TableName("fm_storm_surge_b")
public class StormSurge implements Serializable {

    private static final long serialVersionUID = -53107461437619983L;

    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 监测站ID
     **/
    @TableField("station_id")
    private Long stationId;
    /**
     * 监测站名称
     **/
    @TableField("station_name")
    private String stationName;
    /**
     * 时间
     **/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("time")
    private Date time;
    /**
     * 值
     **/
    @TableField("value")
    private Integer value;
    /**
     * 站点编码
     **/
    @TableField("station_code")
    private String stationCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStationId() {
        return stationId;
    }

    public void setStationId(Long stationId) {
        this.stationId = stationId;
    }

    public String getStationName() {
        return stationName;
    }

    public void setStationName(String stationName) {
        this.stationName = stationName;
    }

    public Date getTime() {
        return time;
    }

    public void setTime(Date time) {
        this.time = time;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getStationCode() {
        return stationCode;
    }

    public void setStationCode(String stationCode) {
        this.stationCode = stationCode;
    }
}



