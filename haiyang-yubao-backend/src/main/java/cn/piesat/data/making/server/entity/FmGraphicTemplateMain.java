package cn.piesat.data.making.server.entity;


import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.*;
import cn.piesat.common.utils.Constant;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 实体类
 *
 * <AUTHOR>
 * @date 2024-10-13 09:43:26
 */
@Data
@Accessors(chain = true)
@TableName("fm_graphic_template_main")
public class FmGraphicTemplateMain implements Serializable {

    private static final long serialVersionUID = -81712580105464126L;


    @TableField("id")
    private Long id;
    /**
     * 图形模版类型
     */
    @TableField("template_type")
    private String templateType;
    /**
     * 产品类型： 预报 警报
     */
    @TableField("product_type")
    private String productType;
    @TableField("name")
    private String name;
    @TableField("product_id")
    private Long productId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTemplateType() {
        return templateType;
    }

    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }
}
