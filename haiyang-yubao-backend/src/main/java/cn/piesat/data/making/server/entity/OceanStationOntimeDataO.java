package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 海洋站整点数据-原始数据实体类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@TableName("ocean_station_ontime_data_o")
public class OceanStationOntimeDataO implements Serializable {

    private static final long serialVersionUID = -58450142600384428L;

    /**
     * id
     **/
    @TableId
    private Long id;

    /**
     * 文件id
     **/
    @JsonProperty("File")
    @TableField("file")
    private Long file;
    /**
     * 行号
     **/
    @JsonProperty("LineNo")
    @TableField("lineno")
    private Integer lineno;
    /**
     * 发报流水号
     **/
    @JsonProperty("send_serial_number")
    @TableField("send_serial_number")
    private String sendSerialNumber;
    /**
     * 发报流水号质控符
     **/
    @JsonProperty("send_serial_number_Qc2")
    @TableField("send_serial_number_qc2")
    private Integer sendSerialNumberQc2;
    /**
     * 发报单位名称代码（站代码）
     **/
    @JsonProperty("send_unit_code")
    @TableField("send_unit_code")
    private String sendUnitCode;
    /**
     * 发报单位名称代码质控符
     **/
    @JsonProperty("send_unit_code_Qc2")
    @TableField("send_unit_code_qc2")
    private Integer sendUnitCodeQc2;
    /**
     * 发报日
     **/
    @JsonProperty("send_date")
    @TableField("send_date")
    private String sendDate;
    /**
     * 发报日质控符
     **/
    @JsonProperty("send_date_Qc2")
    @TableField("send_date_qc2")
    private Integer sendDateQc2;
    /**
     * 地区代码
     **/
    @JsonProperty("area_code")
    @TableField("area_code")
    private String areaCode;
    /**
     * 地区代码质控符
     **/
    @JsonProperty("Area_code_Qc2")
    @TableField("area_code_qc2")
    private Integer areaCodeQc2;
    /**
     * 监测日期 （北京时）
     **/
    @JsonProperty("MonitoringDate")
    @TableField("monitoringdate")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date monitoringdate;
    /**
     * 监测日期 质控符
     **/
    @JsonProperty("MonitoringDate_Qc2")
    @TableField("monitoringdate_qc2")
    private Integer monitoringdateQc2;
    /**
     * 监测日期字符串
     **/
    @JsonProperty("MonitoringDateStr")
    @TableField("monitoringdatestr")
    private String monitoringdatestr;
    /**
     * 监测日期质控符
     **/
    @JsonProperty("MonitoringDateStr_Qc2")
    @TableField("monitoringdatestr_qc2")
    private Integer monitoringdatestrQc2;
    /**
     * 站位号（区站号）
     **/
    @JsonProperty("station_num")
    @TableField("station_num")
    private String stationNum;
    /**
     * 站位号质控符
     **/
    @JsonProperty("station_num_Qc2")
    @TableField("station_num_qc2")
    private Integer stationNumQc2;
    /**
     * 降水组指示码(1为有降水组；3为无降水组；4为有降水没有观测)
     **/
    @JsonProperty("precipitation_indicator_code")
    @TableField("precipitation_indicator_code")
    private String precipitationIndicatorCode;
    /**
     * 降水组指示码(1为有降水组；3为无降水组；4为有降水没有观测)质控符
     **/
    @JsonProperty("precipitation_indicator_code_Qc2")
    @TableField("precipitation_indicator_code_qc2")
    private Integer precipitationIndicatorCodeQc2;
    /**
     * 有效能见度
     **/
    @JsonProperty("effective_visibility")
    @TableField("effective_visibility")
    private String effectiveVisibility;
    /**
     * 有效能见度质控符
     **/
    @JsonProperty("effective_visibility_Qc2")
    @TableField("effective_visibility_qc2")
    private Integer effectiveVisibilityQc2;
    /**
     * 总云量
     **/
    @JsonProperty("total_cloud_cover")
    @TableField("total_cloud_cover")
    private String totalCloudCover;
    /**
     * 总云量质控符
     **/
    @JsonProperty("total_cloud_cover_Qc2")
    @TableField("total_cloud_cover_qc2")
    private Integer totalCloudCoverQc2;
    /**
     * 风向
     **/
    @JsonProperty("wind_dir")
    @TableField("wind_dir")
    private String windDir;
    /**
     * 风向质控符
     **/
    @JsonProperty("wind_dir_Qc2")
    @TableField("wind_dir_qc2")
    private Integer windDirQc2;
    /**
     * 风速
     **/
    @JsonProperty("wind_speed")
    @TableField("wind_speed")
    private String windSpeed;
    /**
     * 风速质控符
     **/
    @JsonProperty("wind_speed_Qc2")
    @TableField("wind_speed_qc2")
    private Integer windSpeedQc2;
    /**
     * 气温组指示码
     **/
    @JsonProperty("temperature_indicator_code")
    @TableField("temperature_indicator_code")
    private String temperatureIndicatorCode;
    /**
     * 气温组指示码质控符
     **/
    @JsonProperty("temperature_indicator_code_Qc2")
    @TableField("temperature_indicator_code_qc2")
    private Integer temperatureIndicatorCodeQc2;
    /**
     * 气温正负指示码
     **/
    @JsonProperty("temperature_sign_indicator_code")
    @TableField("temperature_sign_indicator_code")
    private String temperatureSignIndicatorCode;
    /**
     * 气温正负指示码质控符
     **/
    @JsonProperty("temperature_sign_indicator_code_Qc2")
    @TableField("temperature_sign_indicator_code_qc2")
    private Integer temperatureSignIndicatorCodeQc2;
    /**
     * 气温
     **/
    @JsonProperty("air_temperature")
    @TableField("air_temperature")
    private String airTemperature;
    /**
     * 气温质控符
     **/
    @JsonProperty("air_temperature_Qc2")
    @TableField("air_temperature_qc2")
    private Integer airTemperatureQc2;
    /**
     * 海平面气压指示码
     **/
    @JsonProperty("sea_level_pressure_indicator_code")
    @TableField("sea_level_pressure_indicator_code")
    private String seaLevelPressureIndicatorCode;
    /**
     * 海平面气压指示码质控符
     **/
    @JsonProperty("sea_level_pressure_indicator_code_Qc2")
    @TableField("sea_level_pressure_indicator_code_qc2")
    private Integer seaLevelPressureIndicatorCodeQc2;
    /**
     * 逐时海平面气压
     **/
    @JsonProperty("sea_level_pressure")
    @TableField("sea_level_pressure")
    private String seaLevelPressure;
    /**
     * 逐时海平面气压质控符
     **/
    @JsonProperty("sea_level_pressure_Qc2")
    @TableField("sea_level_pressure_qc2")
    private Integer seaLevelPressureQc2;
    /**
     * 6小时降水组指示码
     **/
    @JsonProperty("precipitation_indicator_code_6H")
    @TableField("precipitation_indicator_code_6h")
    private String precipitationIndicatorCode6h;
    /**
     * 6小时降水组指示码质控符
     **/
    @JsonProperty("precipitation_indicator_code_6H_Qc2")
    @TableField("precipitation_indicator_code_6H_Qc2")
    private Integer precipitationIndicatorCode6hQc2;
    /**
     * 过去6小时降水量，单位毫米
     **/
    @JsonProperty("precipitation_6H")
    @TableField("precipitation_6h")
    private String precipitation6h;
    /**
     * 过去6小时降水量，单位毫米质控符
     **/
    @JsonProperty("precipitation_6H_Qc2")
    @TableField("precipitation_6H_Qc2")
    private Integer precipitation6hQc2;
    /**
     * 海表层水温指示码
     **/
    @JsonProperty("sea_surface_temperature_indicator_code")
    @TableField("sea_surface_temperature_indicator_code")
    private String seaSurfaceTemperatureIndicatorCode;
    /**
     * 海表层水温指示码质控符
     **/
    @JsonProperty("sea_surface_temperature_indicator_code_Qc2")
    @TableField("sea_surface_temperature_indicator_code_qc2")
    private Integer seaSurfaceTemperatureIndicatorCodeQc2;
    /**
     * 海表层水温正负指示码
     **/
    @JsonProperty("sea_surface_temperature_sign_indicator_code")
    @TableField("sea_surface_temperature_sign_indicator_code")
    private String seaSurfaceTemperatureSignIndicatorCode;
    /**
     * 海表层水温正负指示码质控符
     **/
    @JsonProperty("sea_surface_temperature_sign_indicator_code_Qc2")
    @TableField("sea_surface_temperature_sign_indicator_code_qc2")
    private Integer seaSurfaceTemperatureSignIndicatorCodeQc2;
    /**
     * 海表层水温
     **/
    @JsonProperty("sea_surface_temperature")
    @TableField("sea_surface_temperature")
    private String seaSurfaceTemperature;
    /**
     * 海表层水温质控符
     **/
    @JsonProperty("sea_surface_temperature_Qc2")
    @TableField("sea_surface_temperature_qc2")
    private Integer seaSurfaceTemperatureQc2;
    /**
     * 风浪观测方法指示码（1仪测，2目测）
     **/
    @JsonProperty("wind_wave_observation_method_indicator_code")
    @TableField("wind_wave_observation_method_indicator_code")
    private String windWaveObservationMethodIndicatorCode;
    /**
     * 风浪观测方法指示码质控符
     **/
    @JsonProperty("wind_wave_observation_method_indicator_code_Qc2")
    @TableField("wind_wave_observation_method_indicator_code_qc2")
    private Integer windWaveObservationMethodIndicatorCodeQc2;
    /**
     * 浪周期
     **/
    @JsonProperty("wave_period")
    @TableField("wave_period")
    private String wavePeriod;
    /**
     * 浪周期质控符
     **/
    @JsonProperty("wave_period_Qc2")
    @TableField("wave_period_qc2")
    private Integer wavePeriodQc2;
    /**
     * 浪高
     **/
    @JsonProperty("wave_height")
    @TableField("wave_height")
    private String waveHeight;
    /**
     * 浪高质控符
     **/
    @JsonProperty("wave_height_Qc2")
    @TableField("wave_height_qc2")
    private Integer waveHeightQc2;
    /**
     * 涌向组指示码
     **/
    @JsonProperty("surging_towards_indicator_code")
    @TableField("surging_towards_indicator_code")
    private String surgingTowardsIndicatorCode;
    /**
     * 涌向组指示码质控符
     **/
    @JsonProperty("surging_towards_indicator_code_Qc2")
    @TableField("surging_towards_indicator_code_qc2")
    private Integer surgingTowardsIndicatorCodeQc2;
    /**
     * 第一组的涌向
     **/
    @JsonProperty("surging_towards_1")
    @TableField("surging_towards_1")
    private String surgingTowards1;
    /**
     * 第一组的涌向质控符
     **/
    @JsonProperty("surging_towards_1_Qc2")
    @TableField("surging_towards_1_qc2")
    private Integer surgingTowards1Qc2;
    /**
     * 第一组的涌的指示码
     **/
    @JsonProperty("surging_towards_1_indicator_code")
    @TableField("surging_towards_1_indicator_code")
    private String surgingTowards1IndicatorCode;
    /**
     * 第一组的涌的指示码质控符
     **/
    @JsonProperty("surging_towards_1_indicator_code_Qc2")
    @TableField("surging_towards_1_indicator_code_qc2")
    private Integer surgingTowards1IndicatorCodeQc2;
    /**
     * 第一组涌的周期
     **/
    @JsonProperty("surging_towards_1_period")
    @TableField("surging_towards_1_period")
    private String surgingTowards1Period;
    /**
     * 第一组涌的周期质控符
     **/
    @JsonProperty("surging_towards_1_period_Qc2")
    @TableField("surging_towards_1_period_qc2")
    private Integer surgingTowards1PeriodQc2;
    /**
     * 第一组涌的涌高
     **/
    @JsonProperty("surging_towards_1_height")
    @TableField("surging_towards_1_height")
    private String surgingTowards1Height;
    /**
     * 第一组涌的涌高质控符
     **/
    @JsonProperty("surging_towards_1_height_Qc2")
    @TableField("surging_towards_1_height_qc2")
    private Integer surgingTowards1HeightQc2;
    /**
     * 严重冰情报告指示码
     **/
    @JsonProperty("serious_ice_condition_indicator_code")
    @TableField("serious_ice_condition_indicator_code")
    private String seriousIceConditionIndicatorCode;
    /**
     * 严重冰情报告指示码质控符
     **/
    @JsonProperty("serious_ice_condition_indicator_code_Qc2")
    @TableField("serious_ice_condition_indicator_code_qc2")
    private Integer seriousIceConditionIndicatorCodeQc2;
    /**
     * 说明冰情的英文明码
     **/
    @JsonProperty("serious_ice_condition_report")
    @TableField("serious_ice_condition_report")
    private String seriousIceConditionReport;
    /**
     * 说明冰情的英文明码质控符
     **/
    @JsonProperty("serious_ice_condition_report_Qc2")
    @TableField("serious_ice_condition_report_qc2")
    private Integer seriousIceConditionReportQc2;
    /**
     * 特殊风组指示码
     **/
    @JsonProperty("special_wind_indicator_code")
    @TableField("special_wind_indicator_code")
    private String specialWindIndicatorCode;
    /**
     * 特殊风组指示码质控符
     **/
    @JsonProperty("special_wind_indicator_code_Qc2")
    @TableField("special_wind_indicator_code_qc2")
    private Integer specialWindIndicatorCodeQc2;
    /**
     * 极大瞬时风速大于等于17m/s指示码
     **/
    @JsonProperty("wind_speed_gt17_indicator_code")
    @TableField("wind_speed_gt17_indicator_code")
    private String windSpeedGt17IndicatorCode;
    /**
     * 极大瞬时风速大于等于17m/s指示码质控符
     **/
    @JsonProperty("wind_speed_gt17_indicator_code_Qc2")
    @TableField("wind_speed_gt17_indicator_code_qc2")
    private Integer windSpeedGt17IndicatorCodeQc2;
    /**
     * 极大瞬时风速
     **/
    @JsonProperty("max_instant_wind_speed")
    @TableField("max_instant_wind_speed")
    private String maxInstantWindSpeed;
    /**
     * 极大瞬时风速质控符
     **/
    @JsonProperty("max_instant_wind_speed_Qc2")
    @TableField("max_instant_wind_speed_qc2")
    private Integer maxInstantWindSpeedQc2;
    /**
     * 极大瞬时风速对应风向指示码
     **/
    @JsonProperty("max_instant_wind_dir_indicator_code")
    @TableField("max_instant_wind_dir_indicator_code")
    private String maxInstantWindDirIndicatorCode;
    /**
     * 极大瞬时风速对应风向指示码质控符
     **/
    @JsonProperty("max_instant_wind_dir_indicator_code_Qc2")
    @TableField("max_instant_wind_dir_indicator_code_qc2")
    private Integer maxInstantWindDirIndicatorCodeQc2;
    /**
     * 极大瞬时风速对应风向
     **/
    @JsonProperty("max_instant_wind_dir")
    @TableField("max_instant_wind_dir")
    private String maxInstantWindDir;
    /**
     * 极大瞬时风速对应风向质控符
     **/
    @JsonProperty("max_instant_wind_dir_Qc2")
    @TableField("max_instant_wind_dir_qc2")
    private Integer maxInstantWindDirQc2;
    /**
     * 海浪加密观测资料组指示码
     **/
    @JsonProperty("encrypted_observation_ocean_waves_indicator_code")
    @TableField("encrypted_observation_ocean_waves_indicator_code")
    private String encryptedObservationOceanWavesIndicatorCode;
    /**
     * 海浪加密观测资料组指示码质控符
     **/
    @JsonProperty("encrypted_observation_ocean_waves_indicator_code_Qc2")
    @TableField("encrypted_observation_ocean_waves_indicator_code_qc2")
    private Integer encryptedObservationOceanWavesIndicatorCodeQc2;
    /**
     * 海浪加密观测 观测时间（北京时）
     **/
    @JsonProperty("encrypted_observation_ocean_waves_time")
    @TableField("encrypted_observation_ocean_waves_time")
    private String encryptedObservationOceanWavesTime;
    /**
     * 海浪加密观测 观测时间（北京时）质控符
     **/
    @JsonProperty("encrypted_observation_ocean_waves_time_Qc2")
    @TableField("encrypted_observation_ocean_waves_time_qc2")
    private Integer encryptedObservationOceanWavesTimeQc2;
    /**
     * 海浪加密观测 最大浪高
     **/
    @JsonProperty("encrypted_observation_ocean_waves_max_height")
    @TableField("encrypted_observation_ocean_waves_max_height")
    private String encryptedObservationOceanWavesMaxHeight;
    /**
     * 海浪加密观测 最大浪高质控符
     **/
    @JsonProperty("encrypted_observation_ocean_waves_max_height_Qc2")
    @TableField("encrypted_observation_ocean_waves_max_height_qc2")
    private Integer encryptedObservationOceanWavesMaxHeightQc2;
    /**
     * 海冰组指示码
     **/
    @JsonProperty("sea_ice_indicator_code")
    @TableField("sea_ice_indicator_code")
    private String seaIceIndicatorCode;
    /**
     * 海冰组指示码质控符
     **/
    @JsonProperty("sea_ice_indicator_code_Qc2")
    @TableField("sea_ice_indicator_code_qc2")
    private Integer seaIceIndicatorCodeQc2;
    /**
     * 日降雪量,以毫米为单位
     **/
    @JsonProperty("daily_snowfall_amount")
    @TableField("daily_snowfall_amount")
    private String dailySnowfallAmount;
    /**
     * 日降雪量,以毫米为单位质控符
     **/
    @JsonProperty("daily_snowfall_amount_Qc2")
    @TableField("daily_snowfall_amount_qc2")
    private Integer dailySnowfallAmountQc2;
    /**
     * 总冰量
     **/
    @JsonProperty("total_ice_quantity")
    @TableField("total_ice_quantity")
    private String totalIceQuantity;
    /**
     * 总冰量质控符
     **/
    @JsonProperty("total_ice_quantity_Qc2")
    @TableField("total_ice_quantity_qc2")
    private Integer totalIceQuantityQc2;
    /**
     * 流冰量，以“成”为单位
     **/
    @JsonProperty("ice_flow_rate")
    @TableField("ice_flow_rate")
    private String iceFlowRate;
    /**
     * 流冰量，以“成”为单位质控符
     **/
    @JsonProperty("ice_flow_rate_Qc2")
    @TableField("ice_flow_rate_qc2")
    private Integer iceFlowRateQc2;
    /**
     * 浮冰密集度，以“成”为单位
     **/
    @JsonProperty("floating_ice_density")
    @TableField("floating_ice_density")
    private String floatingIceDensity;
    /**
     * 浮冰密集度，以“成”为单位质控符
     **/
    @JsonProperty("floating_ice_density_Qc2")
    @TableField("floating_ice_density_qc2")
    private Integer floatingIceDensityQc2;
    /**
     * 浮冰冰型
     **/
    @JsonProperty("floating_ice_type")
    @TableField("floating_ice_type")
    private String floatingIceType;
    /**
     * 浮冰冰型质控符
     **/
    @JsonProperty("floating_ice_type_Qc2")
    @TableField("floating_ice_type_qc2")
    private Integer floatingIceTypeQc2;
    /**
     * 浮冰冰状
     **/
    @JsonProperty("floating_ice_ice_like")
    @TableField("floating_ice_ice_like")
    private String floatingIceIceLike;
    /**
     * 浮冰冰状质控符
     **/
    @JsonProperty("floating_ice_ice_like_Qc2")
    @TableField("floating_ice_ice_like_qc2")
    private Integer floatingIceIceLikeQc2;
    /**
     * 浮冰漂流速度和方向观测方法指示码0为目测，1位仪器观测
     **/
    @JsonProperty("floating_ice_drifting_speed_dir_indicator_code")
    @TableField("floating_ice_drifting_speed_dir_indicator_code")
    private String floatingIceDriftingSpeedDirIndicatorCode;
    /**
     * 浮冰漂流速度和方向观测方法指示码0为目测，1位仪器观测质控符
     **/
    @JsonProperty("floating_ice_drifting_speed_dir_indicator_code_Qc2")
    @TableField("floating_ice_drifting_speed_dir_indicator_code_qc2")
    private Integer floatingIceDriftingSpeedDirIndicatorCodeQc2;
    /**
     * 浮冰漂流漂流方向
     **/
    @JsonProperty("floating_ice_drifting_dir")
    @TableField("floating_ice_drifting_dir")
    private String floatingIceDriftingDir;
    /**
     * 浮冰漂流漂流方向质控符
     **/
    @JsonProperty("floating_ice_drifting_dir_Qc2")
    @TableField("floating_ice_drifting_dir_qc2")
    private Integer floatingIceDriftingDirQc2;
    /**
     * 浮冰漂流速度，以0.1米/秒为单位
     **/
    @JsonProperty("floating_ice_drifting_speed")
    @TableField("floating_ice_drifting_speed")
    private String floatingIceDriftingSpeed;
    /**
     * 浮冰漂流速度，以0.1米/秒为单位质控符
     **/
    @JsonProperty("floating_ice_drifting_speed_Qc2")
    @TableField("floating_ice_drifting_speed_qc2")
    private Integer floatingIceDriftingSpeedQc2;
    /**
     * 固定冰量
     **/
    @JsonProperty("fixed_ice_quantity")
    @TableField("fixed_ice_quantity")
    private String fixedIceQuantity;
    /**
     * 固定冰量质控符
     **/
    @JsonProperty("fixed_ice_quantity_Qc2")
    @TableField("fixed_ice_quantity_qc2")
    private Integer fixedIceQuantityQc2;
    /**
     * 固定冰型
     **/
    @JsonProperty("fixed_ice_type")
    @TableField("fixed_ice_type")
    private String fixedIceType;
    /**
     * 固定冰型质控符
     **/
    @JsonProperty("fixed_ice_type_Qc2")
    @TableField("fixed_ice_type_qc2")
    private Integer fixedIceTypeQc2;
    /**
     * 固定冰表面特征
     **/
    @JsonProperty("fixed_ice_surface_characteristics")
    @TableField("fixed_ice_surface_characteristics")
    private String fixedIceSurfaceCharacteristics;
    /**
     * 固定冰表面特征质控符
     **/
    @JsonProperty("fixed_ice_surface_characteristics_Qc2")
    @TableField("fixed_ice_surface_characteristics_qc2")
    private Integer fixedIceSurfaceCharacteristicsQc2;
    /**
     * 固定冰堆积量
     **/
    @JsonProperty("fixed_ice_accumulation_amount")
    @TableField("fixed_ice_accumulation_amount")
    private String fixedIceAccumulationAmount;
    /**
     * 固定冰堆积量质控符
     **/
    @JsonProperty("fixed_ice_accumulation_amount_Qc2")
    @TableField("fixed_ice_accumulation_amount_qc2")
    private Integer fixedIceAccumulationAmountQc2;
    /**
     * 固定冰堆积高度
     **/
    @JsonProperty("fixed_ice_accumulation_height")
    @TableField("fixed_ice_accumulation_height")
    private String fixedIceAccumulationHeight;
    /**
     * 固定冰堆积高度质控符
     **/
    @JsonProperty("fixed_ice_accumulation_height_Qc2")
    @TableField("fixed_ice_accumulation_height_qc2")
    private Integer fixedIceAccumulationHeightQc2;
    /**
     * 固定冰宽度和冰厚测点距岸距离，10米为单位
     **/
    @JsonProperty("fixed_ice_distance_shore")
    @TableField("fixed_ice_distance_shore")
    private String fixedIceDistanceShore;
    /**
     * 固定冰宽度和冰厚测点距岸距离，10米为单位质控符
     **/
    @JsonProperty("fixed_ice_distance_shore_Qc2")
    @TableField("fixed_ice_distance_shore_qc2")
    private Integer fixedIceDistanceShoreQc2;
    /**
     * 固定冰厚度，以厘米为单位
     **/
    @JsonProperty("fixed_ice_thickness")
    @TableField("fixed_ice_thickness")
    private String fixedIceThickness;
    /**
     * 固定冰厚度，以厘米为单位质控符
     **/
    @JsonProperty("fixed_ice_thickness_Qc2")
    @TableField("fixed_ice_thickness_qc2")
    private Integer fixedIceThicknessQc2;
    /**
     * 冰温组指示码
     **/
    @JsonProperty("ice_temperature_indicator_code")
    @TableField("ice_temperature_indicator_code")
    private String iceTemperatureIndicatorCode;
    /**
     * 冰温组指示码质控符
     **/
    @JsonProperty("ice_temperature_indicator_code_Qc2")
    @TableField("ice_temperature_indicator_code_qc2")
    private Integer iceTemperatureIndicatorCodeQc2;
    /**
     * 冰温测量层次数
     **/
    @JsonProperty("ice_temperature_measure_num")
    @TableField("ice_temperature_measure_num")
    private String iceTemperatureMeasureNum;
    /**
     * 冰温测量层次数质控符
     **/
    @JsonProperty("ice_temperature_measure_num_Qc2")
    @TableField("ice_temperature_measure_num_qc2")
    private Integer iceTemperatureMeasureNumQc2;
    /**
     * 冰温测量层次，1为表层，2为中层，3为底层
     **/
    @JsonProperty("ice_temperature_measure_hierarchy")
    @TableField("ice_temperature_measure_hierarchy")
    private String iceTemperatureMeasureHierarchy;
    /**
     * 冰温测量层次，1为表层，2为中层，3为底层质控符
     **/
    @JsonProperty("ice_temperature_measure_hierarchy_Qc2")
    @TableField("ice_temperature_measure_hierarchy_qc2")
    private Integer iceTemperatureMeasureHierarchyQc2;
    /**
     * 冰温正负指示
     **/
    @JsonProperty("ice_temperature_sign_indicator_code")
    @TableField("ice_temperature_sign_indicator_code")
    private String iceTemperatureSignIndicatorCode;
    /**
     * 冰温正负指示质控符
     **/
    @JsonProperty("ice_temperature_sign_indicator_code_Qc2")
    @TableField("ice_temperature_sign_indicator_code_qc2")
    private Integer iceTemperatureSignIndicatorCodeQc2;
    /**
     * 冰温
     **/
    @JsonProperty("ice_temperature")
    @TableField("ice_temperature")
    private String iceTemperature;
    /**
     * 冰温质控符
     **/
    @JsonProperty("ice_temperature_Qc2")
    @TableField("ice_temperature_qc2")
    private Integer iceTemperatureQc2;
    /**
     * 潮汐指示码
     **/
    @JsonProperty("tide_indicator_code")
    @TableField("tide_indicator_code")
    private String tideIndicatorCode;
    /**
     * 潮汐指示码质控符
     **/
    @JsonProperty("tide_indicator_code_Qc2")
    @TableField("tide_indicator_code_qc2")
    private Integer tideIndicatorCodeQc2;
    /**
     * 潮汐报告级别，1为每6小时发送一次逐时潮高，2为每3小时发送一次逐时潮高，3为每一小时发送一次逐时潮高
     **/
    @JsonProperty("tide_report_level")
    @TableField("tide_report_level")
    private String tideReportLevel;
    /**
     * 潮汐报告级别，1为每6小时发送一次逐时潮高，2为每3小时发送一次逐时潮高，3为每一小时发送一次逐时潮高质控符
     **/
    @JsonProperty("tide_report_level_Qc2")
    @TableField("tide_report_level_qc2")
    private Integer tideReportLevelQc2;
    /**
     * 潮汐报告时间（北京时）
     **/
    @JsonProperty("tide_report_time")
    @TableField("tide_report_time")
    private String tideReportTime;
    /**
     * 潮汐报告时间（北京时）质控符
     **/
    @JsonProperty("tide_report_time_Qc2")
    @TableField("tide_report_time_qc2")
    private Integer tideReportTimeQc2;
    /**
     * 逐时潮高时间（本整时和YYGG1同）
     **/
    @JsonProperty("hourly_high_tide_time")
    @TableField("hourly_high_tide_time")
    private String hourlyHighTideTime;
    /**
     * 逐时潮高时间（本整时和YYGG1同）质控符
     **/
    @JsonProperty("hourly_high_tide_time_Qc2")
    @TableField("hourly_high_tide_time_qc2")
    private Integer hourlyHighTideTimeQc2;
    /**
     * 潮高，以厘米为单位
     **/
    @JsonProperty("tide_height")
    @TableField("tide_height")
    private String tideHeight;
    /**
     * 潮高，以厘米为单位质控符
     **/
    @JsonProperty("tide_height_Qc2")
    @TableField("tide_height_qc2")
    private Integer tideHeightQc2;
    /**
     * 逐时风速风向指示码
     **/
    @JsonProperty("hourly_wind_speed_dir_indicator_code")
    @TableField("hourly_wind_speed_dir_indicator_code")
    private String hourlyWindSpeedDirIndicatorCode;
    /**
     * 逐时风速风向指示码质控符
     **/
    @JsonProperty("hourly_wind_speed_dir_indicator_code_Qc2")
    @TableField("hourly_wind_speed_dir_indicator_code_qc2")
    private Integer hourlyWindSpeedDirIndicatorCodeQc2;
    /**
     * 逐时风向风速
     **/
    @JsonProperty("hourly_wind_speed_dir")
    @TableField("hourly_wind_speed_dir")
    private String hourlyWindSpeedDir;
    /**
     * 逐时风向风速质控符
     **/
    @JsonProperty("hourly_wind_speed_dir_Qc2")
    @TableField("hourly_wind_speed_dir_qc2")
    private Integer hourlyWindSpeedDirQc2;
    /**
     * 逐时海平面气压指示码
     **/
    @JsonProperty("hourly_sea_level_pressure_indicator_code")
    @TableField("hourly_sea_level_pressure_indicator_code")
    private String hourlySeaLevelPressureIndicatorCode;
    /**
     * 逐时海平面气压指示码质控符
     **/
    @JsonProperty("hourly_sea_level_pressure_indicator_code_Qc2")
    @TableField("hourly_sea_level_pressure_indicator_code_qc2")
    private Integer hourlySeaLevelPressureIndicatorCodeQc2;
    /**
     * 逐时海平面气压
     **/
    @JsonProperty("hourly_sea_level_pressure")
    @TableField("hourly_sea_level_pressure")
    private String hourlySeaLevelPressure;
    /**
     * 逐时海平面气压质控符
     **/
    @JsonProperty("hourly_sea_level_pressure_Qc2")
    @TableField("hourly_sea_level_pressure_qc2")
    private Integer hourlySeaLevelPressureQc2;
    /**
     * 高低潮时、潮高指示码
     **/
    @JsonProperty("max_min_tide_time_height_indicator_code")
    @TableField("max_min_tide_time_height_indicator_code")
    private String maxMinTideTimeHeightIndicatorCode;
    /**
     * 高低潮时、潮高指示码质控符
     **/
    @JsonProperty("max_min_tide_time_height_indicator_code_Qc2")
    @TableField("max_min_tide_time_height_indicator_code_qc2")
    private Integer maxMinTideTimeHeightIndicatorCodeQc2;
    /**
     * 高低潮出现的日期
     **/
    @JsonProperty("max_min_tide_date")
    @TableField("max_min_tide_date")
    private String maxMinTideDate;
    /**
     * 高低潮出现的日期质控符
     **/
    @JsonProperty("max_min_tide_date_Qc2")
    @TableField("max_min_tide_date_qc2")
    private Integer maxMinTideDateQc2;
    /**
     * 高低潮出现的时间（北京时）
     **/
    @JsonProperty("max_min_tide_hour")
    @TableField("max_min_tide_hour")
    private String maxMinTideHour;
    /**
     * 高低潮出现的时间（北京时）质控符
     **/
    @JsonProperty("max_min_tide_hour_Qc2")
    @TableField("max_min_tide_hour_qc2")
    private Integer maxMinTideHourQc2;
    /**
     * 高低潮出现时的分钟
     **/
    @JsonProperty("max_min_tide_minute")
    @TableField("max_min_tide_minute")
    private String maxMinTideMinute;
    /**
     * 高低潮出现时的分钟质控符
     **/
    @JsonProperty("max_min_tide_minute_Qc2")
    @TableField("max_min_tide_minute_qc2")
    private Integer maxMinTideMinuteQc2;
    /**
     * 高低潮潮高
     **/
    @JsonProperty("max_min_tide_height")
    @TableField("max_min_tide_height")
    private String maxMinTideHeight;
    /**
     * 高低潮潮高质控符
     **/
    @JsonProperty("max_min_tide_height_Qc2")
    @TableField("max_min_tide_height_qc2")
    private Integer maxMinTideHeightQc2;
    /**
     * 质量符
     **/
    @JsonProperty("qcflag")
    @TableField("qcflag")
    private Integer qcflag;
    /**
     * 状态
     **/
    @JsonProperty("state")
    @TableField("state")
    private Integer state;
    /**
     * 入库时间
     **/
    @JsonProperty("CreateTime")
    @TableField("createtime")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createtime;
    /**
     * 经度
     **/
    @JsonProperty("LONGITUDE")
    @TableField("longitude")
    private String longitude;
    /**
     * 纬度
     **/
    @JsonProperty("LATITUDE")
    @TableField("latitude")
    private String latitude;
    /**
     * 站点名称
     **/
    @JsonProperty("NAME")
    @TableField("name")
    private String name;
}



