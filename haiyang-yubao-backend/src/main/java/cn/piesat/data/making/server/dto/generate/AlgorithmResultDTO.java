package cn.piesat.data.making.server.dto.generate;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown=true)
public class AlgorithmResultDTO {
    private Integer status;
    private String message;
    private String productionTime;
    private List<Result> result;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getProductionTime() {
        return productionTime;
    }

    public void setProductionTime(String productionTime) {
        this.productionTime = productionTime;
    }

    public List<Result> getResult() {
        return result;
    }

    public void setResult(List<Result> result) {
        this.result = result;
    }

    @JsonIgnoreProperties(ignoreUnknown=true)
    public static class Result{
        private String filePath;
        private String productFormat;
        private String productIdENG;

        public String getFilePath() {
            return filePath;
        }

        public void setFilePath(String filePath) {
            this.filePath = filePath;
        }

        public String getProductFormat() {
            return productFormat;
        }

        public void setProductFormat(String productFormat) {
            this.productFormat = productFormat;
        }

        public String getProductIdENG() {
            return productIdENG;
        }

        public void setProductIdENG(String productIdENG) {
            this.productIdENG = productIdENG;
        }
    }
}
