package cn.piesat.data.making.server.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 船舶数据表DTO类
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ShipDataDTO implements Serializable {

    private static final long serialVersionUID = 792608564308521625L;

    public interface Query {
    }

    public interface RangeQuery {
    }

    /**
     * 开始时间
     **/
    @NotNull(message = "开始时间不能为空", groups = {RangeQuery.class})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 结束时间
     **/
    @NotNull(message = "结束时间不能为空", groups = {RangeQuery.class})
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;
    /**
     * 空间范围
     **/
    @NotBlank(message = "空间范围不能为空", groups = {RangeQuery.class})
    private String geoRange;
    /**
     * 船舶名称
     **/
    @NotBlank(message = "船舶名称不能为空", groups = {Query.class})
    private String signShip;
}



