package cn.piesat.data.making.server.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 潮汐站警戒潮位信息表
 * <AUTHOR>
 */
@Data
@TableName("fm_station_tide_warn_level")
public class StationTideWarnLevel {
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    @TableField("station_id")
    private Long stationId;

    @TableField("warn_height")
    private int warnHeight;

    @TableField("level")
    private int level;

}
