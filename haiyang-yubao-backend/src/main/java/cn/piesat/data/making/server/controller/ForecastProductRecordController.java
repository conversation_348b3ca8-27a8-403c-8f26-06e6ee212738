package cn.piesat.data.making.server.controller;

import cn.piesat.common.syslog.annotation.SysLog;
import cn.piesat.common.syslog.constant.OperateType;
import cn.piesat.data.making.server.model.FileInfo;
import cn.piesat.data.making.server.model.ForecastProductPushResultInfo;
import cn.piesat.data.making.server.model.ProductReleaseResult;
import cn.piesat.data.making.server.service.ForecastProductRecordService;
import cn.piesat.data.making.server.utils.FileUtil;
import cn.piesat.data.making.server.utils.page.PageParam;
import cn.piesat.data.making.server.utils.page.PageResult;
import cn.piesat.data.making.server.vo.ForecastProductRecordVO;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预报产品记录表
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/forecastProductRecord")
public class ForecastProductRecordController {

    @Resource
    private ForecastProductRecordService forecastProductRecordService;

    /**
     * 查询预报产品记录列表
     *
     * @return
     */
    @GetMapping("/list")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public List<ForecastProductRecordVO> getList() {
        return forecastProductRecordService.getList();
    }

    /**
     * 审核预览生成预报产品记录
     *
     * @return
     */
    @GetMapping("/save")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public List<ForecastProductRecordVO> save() {
        return forecastProductRecordService.save();
    }

    /**
     * 发布预报产品
     *
     * @return
     **/
    @GetMapping("/release")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public void release() {
        forecastProductRecordService.release();
    }

    /**
     * 下载
     *
     * @return
     **/
    @GetMapping("/download")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public void download(HttpServletResponse response) {
        forecastProductRecordService.download(response);
    }

    @GetMapping("/downloadDoc/{id}.docx")
    //@SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public void downloadDoc(HttpServletResponse response,@PathVariable("id") Long productRecordId){
        forecastProductRecordService.downloadDoc(response,productRecordId);
    }

    /**
     * 查询预报产品发布结果分页
     *
     * @param pageNum  页数
     * @param pageSize 条数
     * @return
     **/
    @GetMapping("/releaseResult/page")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public PageResult<ForecastProductPushResultInfo> getReleaseResultPage(@RequestParam(defaultValue = "1") Integer pageNum,
                                                                          @RequestParam(defaultValue = "10") Integer pageSize) {
        PageParam pageParam = new PageParam();
        pageParam.setPageNum(pageNum);
        pageParam.setPageSize(pageSize);
        return forecastProductRecordService.getReleaseResultPage(pageParam);
    }

    /**
     * 根据推送任务id、产品名称、状态查询产品列表
     *
     * @param pushTaskId  推送任务id
     * @param productName 产品名称
     * @param state       状态
     * @return
     **/
    @GetMapping("/releaseResult/{pushTaskId}")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.SELECT)
    public List<ProductReleaseResult> getByTaskId(@PathVariable("pushTaskId") Long pushTaskId, @RequestParam(required = false) String productName,
                                                  @RequestParam(required = false) Integer state) {
        return forecastProductRecordService.getReleaseResultByTaskId(pushTaskId, productName, state);
    }

    /**
     * 更新产品文件内容
     *
     * @param fileInfo 文件信息
     * @return
     **/
    @PostMapping("/updateFile")
    @SysLog(systemName = "预报警报制作发布系统", moduleName = "预报产品记录管理", operateType = OperateType.UPDATE)
    public void updateFile(@RequestBody FileInfo fileInfo) {
        FileUtil.updateFile(fileInfo);
    }
}

