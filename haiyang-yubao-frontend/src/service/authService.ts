/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-21 14:42:15
 * @LastEditors: Alex
 * @LastEditTime: 2024-01-18 23:03:50
 * @Description:
 * Copyright (c) 2023 by piesat, All Rights Reserved.
 */
interface AuthInfo {
  token?: string
  realName?: string
  invalidTime?: number
  id?: string
  loginInfo?: any
  userName?: string
  password?: string
  regionCode?: string
  renterId?: string
}

interface UserInfo {
  userName: string
  password: string
}

/**
 * 登录功能服务
 */
export class AuthService {
  public static AuthCache = 'vue3-ts-starter-auth'
  public static UserCache = 'vue3-ts-starter-user'

  /**
   * 保存权限信息
   * @param auth
   * @param expire
   */
  public saveAuth(auth: AuthInfo, expire = 1) {
    const invalidTime = new Date().getTime() + expire * 24 * 60 * 60 * 1000
    const authInfo: AuthInfo = { ...auth, invalidTime }
    localStorage.setItem(AuthService.AuthCache, JSON.stringify(authInfo))
  }

  /**
   * 保存用户、密码
   * @param userName
   * @param password
   */
  public saveUser(userName: string, password: string) {
    localStorage.setItem(
      AuthService.UserCache,
      JSON.stringify({ userName, password })
    )
  }

  /**
   * 获取权限信息
   * @returns
   */
  public getAuth() {
    let info: AuthInfo | undefined
    const authStr = localStorage.getItem(AuthService.AuthCache)
    if (authStr) {
      try {
        const auth: AuthInfo = JSON.parse(authStr)
        if (auth?.invalidTime && auth?.token) {
          const cur = new Date().getTime()
          if (cur < auth.invalidTime) {
            info = auth
          }
        }
      } catch (error) {
        console.error(`解析鉴权信息失败！${error}`)
      }
    }
    if (!info) {
      this.removeAuth()
    }
    return info
  }

  public getUser() {
    let info: UserInfo | undefined
    const userStr = localStorage.getItem(AuthService.UserCache)
    if (userStr) {
      try {
        info = JSON.parse(userStr)
      } catch (error) {
        console.error(`解析用户信息失败！${error}`)
      }
    }
    if (!info) {
      this.removeUser()
    }
    return info
  }

  /**
   * 删除保存的权限信息
   */
  public removeAuth() {
    localStorage.removeItem(AuthService.AuthCache)
  }

  /**
   * 删除保存的用户名和密码
   */
  public removeUser() {
    localStorage.removeItem(AuthService.UserCache)
  }
}
