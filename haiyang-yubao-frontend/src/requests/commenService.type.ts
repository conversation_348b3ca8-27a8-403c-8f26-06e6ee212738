/**
 * 天文潮站点对象
 */
export interface IAstronomicalTideStationItem {
  code: string
  createTime: string
  createUser: null
  createUserId: string
  datumBlueWarn: number
  datumOrangeWarn: number
  datumRedWarn: number
  datumYellowWarn: number
  enable: boolean
  gaugeZeroBlueWarn: number
  gaugeZeroOrangeWarn: number
  gaugeZeroRedWarn: number
  gaugeZeroYellowWarn: number
  id: string
  locationGeo: string
  locationJson: string
  name: string
  regionCode: string
  relationStation: string
  stationTypeCode: string
  tideDatumBlueWarn: number
  tideDatumOrangeWarn: number
  tideDatumRedWarn: number
  tideDatumYellowWarn: number
  type: string
  updateTime: string
  updateUser: string
  updateUserId: string
}

/**
 * 天文大潮站点表格数据
 */
export interface IAstronomicalTideStationRow {
  id: string
  tideTime: string
  tideOnlyTime: string
  tideDate: string // 高潮日期，前端提取自 tideTime 字段
  height: number
  type: number
  stationName: string
  stationId: string
  createTime: string
  updateTime: string
  warnHeight: number
  level: number
  datum: string
  datumBlueDif: number
  datumBlueWarn: number
  yearMonth: null
}

/**
 * 天文潮表格行
 */
export interface IAstronomicalTideStationRow {
  id: string
  tideTime: string
  height: number
  type: number
  stationName: string
  stationId: string
  createTime: string
  updateTime: string
  warnHeight: number
  level: number
  datum: string
  datumBlueDif: number
  datumBlueWarn: number
}

/**
 * 天文潮内容响应 DTO
 */
export interface IAstronomicalTideQueryResDTO {
  // 第一段
  contentF: string | null
  // 第二段
  contentS: string | null
  // 第三段
  contentT: string | null
  createTime: string
  createUser: string
  createUserId: string
  id: string | null
  msgNo: string
  msgTime: string
  publicType: string
  reportTime: string
  saveTime: string
  signMakerId: string | null
  signMakerName: string | null
  signUserId: string | null
  signUserName: string | null
  status: number
  submitTime: string
  taskId: string
  templateCode: string
  templateId: string
  updateTime: string
  updateUser: string
  updateUserId: string
  list: IAstronomicalTideStationRow[] | null
}

/**
 * 天文潮创建内容请求 DTO
 */
export interface IAstronomicalTideCreateOrUpdateReqDTO
  extends Pick<
    IAstronomicalTideQueryResDTO,
    'list' | 'contentF' | 'contentS' | 'contentT' | 'publicType'
  > {}

/**
 * 天文潮内容更新请求 DTO
 */
export interface IAstronomicalTideUpdateReqDTO
  extends Pick<
    IAstronomicalTideQueryResDTO,
    'list' | 'contentF' | 'contentS' | 'contentT' | 'publicType' | 'id'
  > {}

export const enum EWriteFlag {
  SAVE,
  UPDATE
}
