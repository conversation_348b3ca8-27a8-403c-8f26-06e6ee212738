<template>
  <!-- 警报制作 -->
  <div class="alarm-making">
    <base-map
      ref="basemap"
      :show-tool="showTool"
      :current-product="currentProduct"
      @ready="readyMap"
    >
      <AlarmPanel
        ref="alarmPanel"
        @add-forecast="addForecast"
        @change-time="changeTime"
        @save-image="saveImage"
        @close-tool="closeTool"
        @go-map-fit="goMapFit"
      />
    </base-map>
  </div>
</template>

<script setup lang="ts">
import gisUtils from 'src/utils/gis'
import { BaseMap } from 'src/components/OpenlayersMap'
import { AlarmPanel } from './components/index'
import { ref, reactive } from 'vue'
import Api from 'src/requests/forecast'
import AlarmApi from 'src/requests/alarm'
import { useBridge } from 'src/utils/vue-hooks/useBridge/useBridge'
import { IBridge } from 'src/utils/vue-hooks/useBridge/types'
import { bridgeKey, IBridgeData } from './alarmMakingHooks/bridge.type'
const basemap = ref()
const alarmPanel = ref()
const showTool = ref(false)
const currentProduct: any = ref({})
useBridge<IBridge<IBridgeData>>(bridgeKey)
function addForecast(type: any) {
  Api.getTemplateByParent({
    productType: 2,
    status: true,
    templateType: type === 1 ? 'HLJBZZ' : 'FBCJBZZ'
  })
    .then((res: any) => {
      console.log(res, '*********')
      currentProduct.value = res[0]
      showTool.value = true
    })
    .catch(() => {})
}
function changeTime(time: any) {
  basemap.value.changeTime(time)
}
let map: any = null

function goMapFit() {
  Api.getForecastTmpById(currentProduct.value.id)
    .then((res: any) => {
      const extent = [
        res.leftLongitude,
        res.rightLatitude,
        res.rightLongitude,
        res.leftLatitude
      ]
      console.log(extent)
      try {
        map.getView().fit(extent, { padding: [5, 650, 10, 30] })
      } catch (error) {
        console.log(error)
      }
    })
    .catch(() => {})
}
function saveImage() {
  Api.getForecastTmpById(currentProduct.value.id)
    .then((res: any) => {
      const extent = [
        res.leftLongitude,
        res.leftLatitude,
        res.rightLongitude,
        res.rightLatitude
      ]
      map.once('rendercomplete', function () {
        // const leftTopPosition = map.getPixelFromCoordinate([
        //   extent[0],
        //   extent[1]
        // ])
        // // 地理坐标转换屏幕坐标
        // const bottomRightPosition = map.getPixelFromCoordinate([
        //   extent[2],
        //   extent[3]
        // ])
        // // 计算框选矩形的宽度以及高度像素
        // const width = Math.abs(bottomRightPosition[0] - leftTopPosition[0])
        // const height = Math.abs(bottomRightPosition[1] - leftTopPosition[1])
        // // 计算框选矩形的左上角屏幕坐标,放置用户反过来绘制
        // const minx =
        //   leftTopPosition[0] <= bottomRightPosition[0]
        //     ? leftTopPosition[0]
        //     : bottomRightPosition[0]
        // const miny =
        //   leftTopPosition[1] <= bottomRightPosition[1]
        //     ? leftTopPosition[1]
        //     : bottomRightPosition[1]
        //
        // const mapCanvas = document.createElement('canvas')
        // mapCanvas.width = width
        // mapCanvas.height = height
        // let mapContext: any = mapCanvas.getContext('2d')
        // Array.prototype.forEach.call(
        //   map
        //     .getViewport()
        //     .querySelectorAll('.ol-layer canvas, canvas.ol-layer'),
        //   function (canvas) {
        //     if (canvas.width > 0) {
        //       const opacity =
        //         canvas.parentNode.style.opacity || canvas.style.opacity
        //       mapContext.globalAlpha = opacity === '' ? 1 : Number(opacity)
        //       let matrix
        //       const transform = canvas.style.transform
        //       if (transform) {
        //         // Get the transform parameters from the style's transform matrix
        //         matrix = transform
        //           .match(/^matrix\(([^\(]*)\)$/)[1]
        //           .split(',')
        //           .map(Number)
        //       } else {
        //         matrix = [
        //           parseFloat(canvas.style.width) / canvas.width,
        //           0,
        //           0,
        //           parseFloat(canvas.style.height) / canvas.height,
        //           0,
        //           0
        //         ]
        //       }
        //       // Apply the transform to the export map context
        //       CanvasRenderingContext2D.prototype.setTransform.apply(
        //         mapContext,
        //         matrix
        //       )
        //       const backgroundColor = canvas.parentNode.style.backgroundColor
        //       if (backgroundColor) {
        //         mapContext.fillStyle = backgroundColor
        //         mapContext.fillRect(minx, miny, width, height)
        //       }
        //       mapContext.drawImage(canvas, -minx, -miny)
        //     }
        //   }
        // )
        // mapContext.globalAlpha = 1
        // mapContext.setTransform(1, 0, 0, 1, 0, 0)
        // const fileUrlColorful = mapCanvas.toDataURL('image/png')
        const fileUrlColorful = gisUtils.exportImage(map, extent)
        AlarmApi.upLoadBase64Image({
          base64Image: fileUrlColorful
        })
          .then((res: any) => {
            alarmPanel.value.addWarningImage(res)
            alarmPanel.value.closeLoading()
          })
          .catch(() => {
            alarmPanel.value.closeLoading()
          })
      })
      map.renderSync()
    })
    .catch(() => {})
}
function closeTool() {
  showTool.value = false
}
function readyMap(baseMap: any) {
  map = baseMap
}
</script>

<style lang="scss">
.alarm-making {
  width: 100%;
  height: 100%;
  position: relative;
  .tool {
    left: 20px;
  }
}
</style>
