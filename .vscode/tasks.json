{
  "version": "2.0.0",
  "tasks": [
    {
      "label": "Sleep",
      "type": "shell",
      "command": "sleep",
      "args": ["9"],
      "isBackground": true
    },
    {
      "label": "Start PC FE",
      "type": "shell",
      "command": "pnpm",
      "args": ["run", "dev"],
      "isBackground": true,
      "options": {
        "cwd": "${workspaceFolder}/haiyang-yubao-frontend"
      }
    },
    {
      "label": "Before Start PC FE 2",
      "dependsOrder": "sequence",
      "dependsOn": ["Start PC FE", "Sleep"]
      //   "dependsOn": ["Start PC FE"]
    },
    {
      "label": "Before Start PC FE",
      "type": "shell",
      "command": "cd haiyang-yubao-frontend && pnpm run dev && sleep 9",
      "isBackground": true
    }
  ]
}
